import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [{
    ignores: [
      'dist/**',
      'build/**',
      'node_modules/**',
      '.next/**',
      'src/generated/**',
    ],
  },
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules:{
      '@typescript-eslint/no-unused-vars':0,
      '@typescript-eslint/no-explicit-any':0,
      '@typescript-eslint/ban-ts-comment':0,
    }
  }
];

export default eslintConfig;
