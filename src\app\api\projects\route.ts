import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getCurrentUser, hasPermission } from '@/lib/auth'

const createProjectSchema = z.object({
  projectCode: z.string().min(1, '请输入项目编号'),
  projectName: z.string().min(1, '请输入项目名称'),
  sponsor: z.string().optional(),
  studyTitle: z.string().optional(),
  pvEmail: z.string().email('请输入有效的PV邮箱').optional(),
  userIds: z.array(z.number()).optional(),
  userRoles: z.array(z.object({
    userId: z.number(),
    roleInProject: z.string(),
    isPrimary: z.boolean()
  })).optional()
})

const updateProjectSchema = createProjectSchema.partial().extend({
  id: z.number(),
  isActive: z.boolean().optional()
})

/**
 * 获取项目列表
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const search = url.searchParams.get('search') || ''
    const userOnly = url.searchParams.get('userOnly') === 'true'

    const skip = (page - 1) * limit

    const where: any = {
      AND: [
        search ? {
          OR: [
            { projectCode: { contains: search } },
            { projectName: { contains: search } },
            { sponsor: { contains: search } }
          ]
        } : {}
      ]
    }

    // 如果是普通用户或者指定了userOnly，只返回用户有权限的项目
    if (userOnly || !hasPermission(user.roles, ['PM'])) {
      where.AND.push({
        userProjects: {
          some: {
            userId: user.id
          }
        }
      })
    }

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          userProjects: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  roles: true
                }
              }
            }
          },
          _count: {
            select: {
              saeReports: true,
              emailTemplates: true
            }
          }
        }
      }),
      prisma.project.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        projects: projects.map(project => ({
          ...project,
          users: project.userProjects.map(up => ({
            ...up.user,
            roleInProject: up.roleInProject,
            isPrimary: up.isPrimary
          })),
          stats: {
            reportCount: project._count.saeReports,
            templateCount: project._count.emailTemplates
          }
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get projects failed:', error)
    return NextResponse.json({
      success: false,
      error: '获取项目列表失败'
    }, { status: 500 })
  }
}

/**
 * 创建项目
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const { projectCode, projectName, sponsor, studyTitle, pvEmail, userIds, userRoles } = createProjectSchema.parse(body)

    // 检查项目编号是否已存在
    const existingProject = await prisma.project.findUnique({
      where: { projectCode }
    })

    if (existingProject) {
      return NextResponse.json({
        success: false,
        error: '该项目编号已存在'
      }, { status: 400 })
    }

    // 创建项目
    const newProject = await prisma.project.create({
      data: {
        projectCode,
        projectName,
        sponsor,
        studyTitle,
        pvEmail
      }
    })

    // 如果指定了用户角色信息，创建用户项目关联
    if (userRoles && userRoles.length > 0) {
      await prisma.userProject.createMany({
        data: userRoles.map(ur => ({
          userId: ur.userId,
          projectId: newProject.id,
          roleInProject: ur.roleInProject as any, // 转换为ProjectRole枚举
          isPrimary: ur.isPrimary
        }))
      })
    } else if (userIds && userIds.length > 0) {
      // 兼容旧的userIds方式
      const users = await prisma.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, roles: true }
      })

      await prisma.userProject.createMany({
        data: users.map(u => ({
          userId: u.id,
          projectId: newProject.id,
          roleInProject: 'Triage' as any, // 默认角色
          isPrimary: false
        }))
      })
    }

    // 获取完整的项目信息
    const projectWithUsers = await prisma.project.findUnique({
      where: { id: newProject.id },
      include: {
        userProjects: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                roles: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        project: {
          ...projectWithUsers,
          users: projectWithUsers?.userProjects.map(up => ({
            ...up.user,
            roleInProject: up.roleInProject,
            isPrimary: up.isPrimary
          }))
        }
      },
      message: '项目创建成功'
    })

  } catch (error) {
    console.error('Create project failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: '创建项目失败'
    }, { status: 500 })
  }
}

/**
 * 更新项目
 */
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const { id, projectCode, projectName, sponsor, studyTitle, pvEmail, isActive, userIds, userRoles } = updateProjectSchema.parse(body)

    // 检查项目是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json({
        success: false,
        error: '项目不存在'
      }, { status: 404 })
    }

    // 如果更新项目编号，检查是否重复
    if (projectCode && projectCode !== existingProject.projectCode) {
      const codeExists = await prisma.project.findUnique({
        where: { projectCode }
      })

      if (codeExists) {
        return NextResponse.json({
          success: false,
          error: '该项目编号已存在'
        }, { status: 400 })
      }
    }

    // 更新项目基本信息
    await prisma.project.update({
      where: { id },
      data: {
        ...(projectCode && { projectCode }),
        ...(projectName && { projectName }),
        ...(sponsor !== undefined && { sponsor }),
        ...(studyTitle !== undefined && { studyTitle }),
        ...(pvEmail !== undefined && { pvEmail }),
        ...(isActive !== undefined && { isActive })
      }
    })

    // 如果指定了用户角色信息，更新用户项目关联
    if (userRoles !== undefined) {
      // 删除现有关联
      await prisma.userProject.deleteMany({
        where: { projectId: id }
      })

      // 创建新关联
      if (userRoles.length > 0) {
        await prisma.userProject.createMany({
          data: userRoles.map(ur => ({
            userId: ur.userId,
            projectId: id,
            roleInProject: ur.roleInProject as any, // 转换为ProjectRole枚举
            isPrimary: ur.isPrimary
          }))
        })
      }
    } else if (userIds !== undefined) {
      // 兼容旧的userIds方式
      // 删除现有关联
      await prisma.userProject.deleteMany({
        where: { projectId: id }
      })

      // 创建新关联
      if (userIds.length > 0) {
        const users = await prisma.user.findMany({
          where: { id: { in: userIds } },
          select: { id: true, roles: true }
        })

        await prisma.userProject.createMany({
          data: users.map(u => ({
            userId: u.id,
            projectId: id,
            roleInProject: 'Triage' as any, // 默认角色
            isPrimary: false
          }))
        })
      }
    }

    // 获取完整的项目信息
    const projectWithUsers = await prisma.project.findUnique({
      where: { id },
      include: {
        userProjects: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                roles: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        project: {
          ...projectWithUsers,
          users: projectWithUsers?.userProjects.map(up => ({
            ...up.user,
            roleInProject: up.roleInProject,
            isPrimary: up.isPrimary
          }))
        }
      },
      message: '项目更新成功'
    })

  } catch (error) {
    console.error('Update project failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: '更新项目失败'
    }, { status: 500 })
  }
}
