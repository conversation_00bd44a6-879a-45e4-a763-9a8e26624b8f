'use client'

import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { formatDate, formatDuration } from '@/lib/utils'

interface ExecutionLog {
  id: string
  startTime: string
  endTime?: string
  duration?: number
  status: 'running' | 'completed' | 'failed'
  processedCount: number
  failedCount: number
  error?: string
  triggeredBy?: string
}

interface TaskExecutionModalProps {
  isOpen: boolean
  onClose: () => void
  execution: ExecutionLog | null
}

export function TaskExecutionModal({ isOpen, onClose, execution }: TaskExecutionModalProps) {
  if (!execution) return null

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <span className="sr-only">关闭</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="w-full">
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900 mb-4">
                      任务执行详情
                    </Dialog.Title>

                    <div className="space-y-6">
                      {/* 基本信息 */}
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">基本信息</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <span className="text-sm text-gray-500">执行ID</span>
                            <p className="text-sm font-medium text-gray-900 break-all">
                              {execution.id}
                            </p>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">触发者</span>
                            <p className="text-sm font-medium text-gray-900">
                              {execution.triggeredBy || '系统自动'}
                            </p>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">开始时间</span>
                            <p className="text-sm font-medium text-gray-900">
                              {formatDate(execution.startTime)}
                            </p>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">结束时间</span>
                            <p className="text-sm font-medium text-gray-900">
                              {execution.endTime ? formatDate(execution.endTime) : '未结束'}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* 执行状态 */}
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">执行状态</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <span className="text-sm text-gray-500">状态</span>
                            <div className="mt-1">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                execution.status === 'completed' 
                                  ? 'bg-green-100 text-green-800'
                                  : execution.status === 'failed'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {execution.status === 'completed' ? '已完成' :
                                 execution.status === 'failed' ? '失败' : '运行中'}
                              </span>
                            </div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">执行时长</span>
                            <p className="text-sm font-medium text-gray-900">
                              {execution.duration ? formatDuration(execution.duration) : '计算中...'}
                            </p>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">总任务数</span>
                            <p className="text-sm font-medium text-gray-900">
                              {execution.processedCount + execution.failedCount}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* 处理结果 */}
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">处理结果</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-sm text-gray-500">成功处理</span>
                            <span className="text-sm font-medium text-green-600">
                              {execution.processedCount} 个任务
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span className="text-sm text-gray-500">处理失败</span>
                            <span className="text-sm font-medium text-red-600">
                              {execution.failedCount} 个任务
                            </span>
                          </div>
                        </div>
                        
                        {/* 成功率 */}
                        <div className="mt-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">成功率</span>
                            <span className="font-medium">
                              {execution.processedCount + execution.failedCount > 0 
                                ? ((execution.processedCount / (execution.processedCount + execution.failedCount)) * 100).toFixed(1)
                                : '0'
                              }%
                            </span>
                          </div>
                          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${execution.processedCount + execution.failedCount > 0 
                                  ? (execution.processedCount / (execution.processedCount + execution.failedCount)) * 100
                                  : 0
                                }%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>

                      {/* 错误信息 */}
                      {execution.error && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                          <h4 className="text-sm font-medium text-red-900 mb-2">错误信息</h4>
                          <p className="text-sm text-red-700 break-words">
                            {execution.error}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="mt-6 flex justify-end">
                      <button
                        type="button"
                        className="inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                        onClick={onClose}
                      >
                        关闭
                      </button>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
