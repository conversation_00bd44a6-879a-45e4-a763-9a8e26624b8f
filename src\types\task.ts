import type { Task as _Task } from 'generated-prisma'

export interface TaskEmailData {
  itemId: string;
  sender: string;
  receivedTime: string;
  subject: string;
  // 完整邮件信息（从getCurEmailDetails获取）
  from?: string;
  to?: string;
  cc?: string;
  date?: string;
  body?: string;
  attachments?: Array<{
    name: string;
    size: number;
    isInline: boolean;
    id: string;
    contentType: string;
    content: string; // base64
    encoding: string;
  }>;
}

export type TaskMetadata = TaskEmailData & {
  source: 'outlook'
  analysis?: any;
  lastError?: string;
  lastErrorTime?: string;
  debugMode?: boolean;
  processedAt?: string;
  debugProcessedAt?: string;
  recipients?: string[];
  attachments?: Array<{
    name: string;
    size: number;
    contentType: string;
    isInline?: boolean;
    id?: string;
    encoding?: string;
  }>;
  hasFullContent?: boolean; // 标记是否包含完整邮件内容
}
 