'use client'

import { useEffect, useState } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/Button'
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell, TableActionHead, TableActionCell } from '@/components/ui/Table'
import { ProjectForm } from '@/components/forms/ProjectForm'
import { formatDate, getRoleText } from '@/lib/utils'

interface Project {
  id: number
  projectCode: string
  projectName: string
  sponsor?: string
  studyTitle?: string
  pvEmail?: string
  isActive: boolean
  createdAt: string
  users: Array<{
    id: number
    name: string
    email: string
    roles: string[]
    roleInProject: string
    isPrimary: boolean
  }>
  stats: {
    reportCount: number
    templateCount: number
  }
}

interface ProjectsResponse {
  success: boolean
  data: {
    projects: Project[]
    error: string;
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  // 表单状态
  const [showForm, setShowForm] = useState(false)
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')

  useEffect(() => {
    loadProjects()
  }, [page, search])

  const loadProjects = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search })
      })

      const response = await fetch(`/api/projects?${params}`, {
        credentials: 'include'
      })

      const result: ProjectsResponse = await response.json()

      if (result.success) {
        setProjects(result.data.projects)
        setPagination(result.data.pagination)
      } else {
        setError(result.data?.error || '获取项目列表失败')
      }
    } catch (error) {
      console.error('Load projects failed:', error)
      setError('获取项目列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPage(1)
    loadProjects()
  }

  const handleCreateProject = () => {
    setEditingProject(null)
    setFormMode('create')
    setShowForm(true)
  }

  const handleEditProject = (project: Project) => {
    setEditingProject(project)
    setFormMode('edit')
    setShowForm(true)
  }

  const handleDeleteProject = async (project: Project) => {
    if (!confirm(`确定要删除项目 "${project.projectName}" 吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/projects`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          id: project.id,
          isActive: false
        })
      })

      const result = await response.json()

      if (result.success) {
        loadProjects() // 重新加载列表
      } else {
        setError(result.error || '删除项目失败')
      }
    } catch (error) {
      console.error('Delete project failed:', error)
      setError('删除项目失败')
    }
  }

  const handleFormSubmit = async (projectData: Partial<Project>) => {
    try {
      const url = '/api/projects'
      const method = formMode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(projectData)
      })

      const result = await response.json()

      if (result.success) {
        loadProjects() // 重新加载列表
        setShowForm(false)
      } else {
        throw new Error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('Form submit failed:', error)
      throw error
    }
  }

  if (isLoading && projects.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">项目管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理所有临床试验项目和相关信息
          </p>
        </div>
        <Button onClick={handleCreateProject}>
          新建项目
        </Button>
      </div>

      {/* 搜索栏 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="搜索项目编号、项目名称或申办方..."
              className="form-input w-full"
            />
          </div>
          <Button type="submit">
            搜索
          </Button>
        </form>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* 项目列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {projects.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>项目信息</TableHead>
                <TableHead>申办方</TableHead>
                <TableHead>团队成员</TableHead>
                <TableHead>统计</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableActionHead>操作</TableActionHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {projects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {project.projectCode}
                      </div>
                      <div className="text-sm text-gray-500">
                        {project.projectName}
                      </div>
                      {project.pvEmail && (
                        <div className="text-xs text-blue-600">
                          PV: {project.pvEmail}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {project.sponsor || '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {project.users.slice(0, 3).map((user) => (
                        <span
                          key={user.id}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {user.name} ({getRoleText(user.roleInProject)})
                        </span>
                      ))}
                      {project.users.length > 3 && (
                        <span className="text-xs text-gray-500">
                          +{project.users.length - 3} 更多
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>报告: {project.stats.reportCount}</div>
                      <div>模板: {project.stats.templateCount}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      project.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {project.isActive ? '活跃' : '停用'}
                    </span>
                  </TableCell>
                  <TableCell>
                    {formatDate(project.createdAt)}
                  </TableCell>
                  <TableActionCell>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditProject(project)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        variant="danger"
                        onClick={() => handleDeleteProject(project)}
                      >
                        删除
                      </Button>
                    </div>
                  </TableActionCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-8">
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无项目</h3>
            <p className="mt-1 text-sm text-gray-500">
              还没有创建任何项目
            </p>
            <div className="mt-6">
              <Button onClick={handleCreateProject}>
                创建第一个项目
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              上一页
            </button>
            <button
              onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
              disabled={page === pagination.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              下一页
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                显示第 <span className="font-medium">{(page - 1) * pagination.limit + 1}</span> 到{' '}
                <span className="font-medium">
                  {Math.min(page * pagination.limit, pagination.total)}
                </span>{' '}
                条，共 <span className="font-medium">{pagination.total}</span> 条记录
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
                  disabled={page === pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* 项目表单 */}
      <ProjectForm
        isOpen={showForm}
        onClose={() => setShowForm(false)}
        onSubmit={handleFormSubmit}
        project={editingProject}
        mode={formMode}
      />
    </div>
  )
}
