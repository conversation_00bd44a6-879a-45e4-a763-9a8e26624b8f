import { NextRequest, NextResponse } from 'next/server'
import { apiLog } from '@/lib/logger'

export interface LoggerMiddlewareOptions {
  // 是否记录请求体（可能包含敏感信息）
  logRequestBody?: boolean
  // 是否记录响应体
  logResponseBody?: boolean
  // 需要过滤的敏感字段
  sensitiveFields?: string[]
  // 跳过日志记录的路径
  skipPaths?: string[]
}

const defaultOptions: LoggerMiddlewareOptions = {
  logRequestBody: false,
  logResponseBody: false,
  sensitiveFields: ['password', 'token', 'authorization', 'cookie'],
  skipPaths: ['/api/health', '/favicon.ico', '/_next']
}

/**
 * 过滤敏感信息
 */
function filterSensitiveData(data: any, sensitiveFields: string[]): any {
  if (!data || typeof data !== 'object') {
    return data
  }

  const filtered = { ...data }
  
  for (const field of sensitiveFields) {
    if (field in filtered) {
      filtered[field] = '[FILTERED]'
    }
  }
  
  return filtered
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

/**
 * 获取用户代理信息
 */
function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown'
}

/**
 * API日志中间件
 */
export function createLoggerMiddleware(options: LoggerMiddlewareOptions = {}) {
  const config = { ...defaultOptions, ...options }
  
  return async function loggerMiddleware(
    request: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> {
    const startTime = Date.now()
    const method = request.method
    const url = request.url
    const pathname = new URL(url).pathname
    
    // 检查是否跳过日志记录
    if (config.skipPaths?.some(path => pathname.startsWith(path))) {
      return handler(request)
    }
    
    const clientIP = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    // 准备请求日志数据
    const requestMeta: any = {
      ip: clientIP,
      userAgent,
      pathname
    }
    
    // 记录请求体（如果启用）
    if (config.logRequestBody && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      try {
        const body = await request.clone().json()
        requestMeta.body = filterSensitiveData(body, config.sensitiveFields || [])
      } catch {
        // 如果不是JSON，尝试获取文本
        try {
          const text = await request.clone().text()
          if (text && text.length < 1000) { // 限制文本长度
            requestMeta.body = text
          }
        } catch {
          // 忽略错误
        }
      }
    }
    
    // 记录请求头（过滤敏感信息）
    const headers: Record<string, string> = {}
    request.headers.forEach((value, key) => {
      if (!config.sensitiveFields?.includes(key.toLowerCase())) {
        headers[key] = value
      } else {
        headers[key] = '[FILTERED]'
      }
    })
    requestMeta.headers = headers
    
    // 记录请求开始
    apiLog.request(method, pathname, requestMeta)
    
    try {
      // 执行实际的处理函数
      const response = await handler(request)
      const duration = Date.now() - startTime
      const status = response.status
      
      // 准备响应日志数据
      const responseMeta: any = {
        ip: clientIP,
        pathname,
        duration: `${duration}ms`
      }
      
      // 记录响应体（如果启用且状态码表示成功）
      if (config.logResponseBody && status >= 200 && status < 300) {
        try {
          const responseClone = response.clone()
          const contentType = response.headers.get('content-type')
          
          if (contentType?.includes('application/json')) {
            const body = await responseClone.json()
            responseMeta.body = filterSensitiveData(body, config.sensitiveFields || [])
          }
        } catch {
          // 忽略错误
        }
      }
      
      // 记录响应
      apiLog.response(method, pathname, status, duration, responseMeta)
      
      return response
      
    } catch (error) {
      const duration = Date.now() - startTime
      
      // 记录错误
      apiLog.error(method, pathname, error as Error, {
        ip: clientIP,
        pathname,
        duration: `${duration}ms`
      })
      
      throw error
    }
  }
}

/**
 * 默认的日志中间件实例
 */
export const loggerMiddleware = createLoggerMiddleware()

/**
 * 用于开发环境的详细日志中间件
 */
export const detailedLoggerMiddleware = createLoggerMiddleware({
  logRequestBody: true,
  logResponseBody: true,
  sensitiveFields: ['password', 'token', 'authorization', 'cookie', 'session']
})

/**
 * 用于生产环境的简化日志中间件
 */
export const productionLoggerMiddleware = createLoggerMiddleware({
  logRequestBody: false,
  logResponseBody: false,
  skipPaths: ['/api/health', '/favicon.ico', '/_next', '/api/metrics']
})
