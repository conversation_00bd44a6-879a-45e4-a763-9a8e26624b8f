'use client'

import { useEffect, useState } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate, getStatusText, getReportTypeText } from '@/lib/utils'

interface SAEReport {
  id: number
  reportUuid: string
  protocolNumber?: string
  subjectNumber?: string 
  eventName?: string
  reportType: string
  learnedDate?: string
  seriousness?: string
  eventType?: string
  causality?: string 
  status: string
  createdAt: string
  project: {
    id: number
    projectCode: string
    projectName: string
  }
}

export default function ReportsPage() {
  const [reports, setReports] = useState<SAEReport[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [projectFilter, setProjectFilter] = useState('')

  useEffect(() => {
    loadReports()
  }, [search, statusFilter, projectFilter])

  const loadReports = async () => {
    try {
      setIsLoading(true)
      // 这里应该调用实际的API
      // 暂时使用模拟数据
      setTimeout(() => {
        const mockReports: SAEReport[] = [
          {
            id: 1,
            reportUuid: 'uuid-1',
            protocolNumber: 'MRG004A-001',
            subjectNumber: '001-001', 
            eventName: '严重不良事件',
            reportType: 'INITIAL',
            learnedDate: '2024-01-15',
            seriousness: '危及生命',
            eventType: '非预期',
            causality: '可能相关', 
            status: 'PROCESSING',
            createdAt: new Date().toISOString(),
            project: {
              id: 1,
              projectCode: 'MRG004A-001',
              projectName: 'MRG004A临床试验'
            }
          },
          {
            id: 2,
            reportUuid: 'uuid-2',
            protocolNumber: 'ABC123-002',
            subjectNumber: '002-001', 
            eventName: '药物不良反应',
            reportType: 'FOLLOW_UP',
            learnedDate: '2024-01-14',
            seriousness: '严重',
            eventType: '预期',
            causality: '很可能相关', 
            status: 'COMPLETED',
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            project: {
              id: 2,
              projectCode: 'ABC123-002',
              projectName: 'ABC123临床试验'
            }
          }
        ]

        // 应用筛选
        let filteredReports = mockReports
        if (search) {
          filteredReports = filteredReports.filter(report => 
            report.protocolNumber?.includes(search) ||
            report.subjectNumber?.includes(search) ||
            report.eventName?.includes(search) ||
            report.project.projectCode.includes(search)
          )
        }
        if (statusFilter) {
          filteredReports = filteredReports.filter(report => report.status === statusFilter)
        }

        setReports(filteredReports)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Load reports failed:', error)
      setError('获取报告列表失败')
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    loadReports()
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">SAE报告</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理和处理所有SAE报告
          </p>
        </div> 
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="搜索方案编号、受试者编号、事件名称..."
              className="form-input w-full"
            />
          </div>
          <div className="w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-input w-full"
            >
              <option value="">所有状态</option>
              <option value="RECEIVED">已接收</option>
              <option value="PROCESSING">处理中</option>
              <option value="CONFIRMED">已确认</option>
              <option value="FORWARDED">已转发</option>
              <option value="COMPLETED">已完成</option>
            </select>
          </div>
          <button type="submit" className="btn btn-primary">
            搜索
          </button>
        </form>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* 报告列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {reports.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    报告信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    项目
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    事件详情
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    严重性评估
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {report.protocolNumber || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          受试者: {report.subjectNumber || 'N/A'}
                        </div>
                        <div className="text-xs text-blue-600">
                          {getReportTypeText(report.reportType)}
                        </div>
                        {report.learnedDate && (
                          <div className="text-xs text-gray-400">
                            获知日期: {report.learnedDate}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {report.project.projectCode}
                        </div>
                        <div className="text-sm text-gray-500">
                          {report.project.projectName}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {report.eventName || 'N/A'}
                        </div>
                        {report.eventType && (
                          <div className="text-sm text-gray-500">
                            类型: {report.eventType}
                          </div>
                        )} 
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        {report.seriousness && (
                          <div className="text-sm font-medium text-red-600">
                            {report.seriousness}
                          </div>
                        )}
                        {report.causality && (
                          <div className="text-sm text-gray-500">
                            因果关系: {report.causality}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        report.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                        report.status === 'PROCESSING' ? 'bg-yellow-100 text-yellow-800' :
                        report.status === 'RECEIVED' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {getStatusText(report.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(report.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          查看
                        </button> 
                        <button className="text-purple-600 hover:text-purple-900">
                          下载
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无报告</h3>
            <p className="mt-1 text-sm text-gray-500">
              还没有收到任何SAE报告
            </p>
            <div className="mt-6">
              <button className="btn btn-primary">
                手动创建第一个报告
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">📊</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    总报告数
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {reports.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">⏳</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    处理中
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {reports.filter(r => r.status === 'PROCESSING').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">✅</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    已完成
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {reports.filter(r => r.status === 'COMPLETED').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">🚨</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    严重事件
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {reports.filter(r => r.seriousness?.includes('危及生命')).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
