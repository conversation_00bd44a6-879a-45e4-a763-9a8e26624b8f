@import "tailwindcss";

/* Tailwind v4 Theme Configuration */
@theme {
  --color-border: hsl(214.3 31.8% 91.4%);
  --color-input: hsl(214.3 31.8% 91.4%);
  --color-ring: hsl(221.2 83.2% 53.3%);
  --color-background: hsl(0 0% 100%);
  --color-foreground: hsl(222.2 84% 4.9%);

  --color-primary: hsl(221.2 83.2% 53.3%);
  --color-primary-foreground: hsl(210 40% 98%);

  --color-secondary: hsl(210 40% 96%);
  --color-secondary-foreground: hsl(222.2 84% 4.9%);

  --color-destructive: hsl(0 84.2% 60.2%);
  --color-destructive-foreground: hsl(210 40% 98%);

  --color-muted: hsl(210 40% 96%);
  --color-muted-foreground: hsl(215.4 16.3% 46.9%);

  --color-accent: hsl(210 40% 96%);
  --color-accent-foreground: hsl(222.2 84% 4.9%);

  --color-popover: hsl(0 0% 100%);
  --color-popover-foreground: hsl(222.2 84% 4.9%);

  --color-card: hsl(0 0% 100%);
  --color-card-foreground: hsl(222.2 84% 4.9%);

  --radius: 0.5rem;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
}

/* Dark mode theme */
@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: hsl(222.2 84% 4.9%);
    --color-foreground: hsl(210 40% 98%);
    --color-card: hsl(222.2 84% 4.9%);
    --color-card-foreground: hsl(210 40% 98%);
    --color-popover: hsl(222.2 84% 4.9%);
    --color-popover-foreground: hsl(210 40% 98%);
    --color-primary: hsl(217.2 91.2% 59.8%);
    --color-primary-foreground: hsl(222.2 84% 4.9%);
    --color-secondary: hsl(217.2 32.6% 17.5%);
    --color-secondary-foreground: hsl(210 40% 98%);
    --color-muted: hsl(217.2 32.6% 17.5%);
    --color-muted-foreground: hsl(215 20.2% 65.1%);
    --color-accent: hsl(217.2 32.6% 17.5%);
    --color-accent-foreground: hsl(210 40% 98%);
    --color-destructive: hsl(0 62.8% 30.6%);
    --color-destructive-foreground: hsl(210 40% 98%);
    --color-border: hsl(217.2 32.6% 17.5%);
    --color-input: hsl(217.2 32.6% 17.5%);
    --color-ring: hsl(224.3 76.3% 94.1%);
  }
}

/* Base styles */
* {
  border-color: var(--color-border);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
}

/* Custom component styles */
.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
}

.sidebar-nav-item:hover {
  background-color: rgb(243 244 246);
  color: rgb(17 24 39);
}

.sidebar-nav-item.active {
  background-color: rgb(219 234 254);
  color: rgb(29 78 216);
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  border: 1px solid rgb(229 231 235);
}

.card-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgb(229 231 235);
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgb(17 24 39);
}

.card-content {
  padding: 1.5rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-ring);
}

.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
}

.btn-secondary {
  background-color: rgb(229 231 235);
  color: rgb(17 24 39);
}

.btn-secondary:hover {
  background-color: rgb(209 213 219);
}

.btn-danger {
  background-color: rgb(220 38 38);
  color: white;
}

.btn-danger:hover {
  background-color: rgb(185 28 28);
}

.btn-success {
  background-color: rgb(22 163 74);
  color: white;
}

.btn-success:hover {
  background-color: rgb(21 128 61);
}
.form-input {
  color: rgb(17 24 39);
  background-color: white;
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  font-size: 0.875rem;
}

.form-input::placeholder {
  color: rgb(156 163 175);
}

.form-input:focus {
  outline: none;
  border-color: rgb(59 130 246);
  box-shadow: 0 0 0 1px rgb(59 130 246);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55 65 81);
  margin-bottom: 0.25rem;
}

.form-error {
  font-size: 0.875rem;
  color: rgb(220 38 38);
  margin-top: 0.25rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.badge-warning {
  background-color: rgb(254 249 195);
  color: rgb(133 77 14);
}

.badge-danger {
  background-color: rgb(254 226 226);
  color: rgb(153 27 27);
}

.badge-info {
  background-color: rgb(219 234 254);
  color: rgb(30 64 175);
}

.badge-gray {
  background-color: rgb(243 244 246);
  color: rgb(55 65 81);
}

.table {
  min-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table-header {
  background-color: rgb(249 250 251);
}

.table-header-cell {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: rgb(107 114 128);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid rgb(229 231 235);
}

.table-body {
  background-color: white;
}

.table-cell {
  padding: 1rem 1.5rem;
  white-space: nowrap;
  font-size: 0.875rem;
  color: rgb(17 24 39);
  border-bottom: 1px solid rgb(229 231 235);
}

.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  border: 2px solid rgb(209 213 219);
  border-top-color: rgb(37 99 235);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    @apply hidden;
  }

  .main-content {
    @apply ml-0;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    @apply hidden;
  }

  .print-break {
    page-break-before: always;
  }
}
