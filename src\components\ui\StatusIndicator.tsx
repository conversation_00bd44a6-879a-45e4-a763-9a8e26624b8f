'use client'

import { useEffect, useState } from 'react'

interface StatusIndicatorProps {
  isActive: boolean
  activeText?: string
  inactiveText?: string
  showPulse?: boolean
}

export function StatusIndicator({ 
  isActive, 
  activeText = '运行中', 
  inactiveText = '空闲',
  showPulse = true 
}: StatusIndicatorProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {inactiveText}
      </span>
    )
  }

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
      isActive 
        ? 'bg-blue-100 text-blue-800' 
        : 'bg-green-100 text-green-800'
    }`}>
      {showPulse && isActive && (
        <span className="flex relative h-2 w-2 mr-1.5">
          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
          <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
        </span>
      )}
      {isActive ? activeText : inactiveText}
    </span>
  )
}
