import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Separator } from '@/components/ui/Separator';
import { Send, RefreshCw } from 'lucide-react';
import { Project, UserInfo } from '../types';

interface ProjectListProps {
  projects: Project[]; 
}

export function ProjectList({
  projects, 
}: ProjectListProps) { 
 

  if (projects.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">项目列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground text-sm">
            暂无可用项目
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">
          项目列表 ({projects.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {projects.map((project, index) => (
          <div key={project.id}>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-black truncate">
                    {project.name}
                  </h4>
                  <p className="text-xs text-muted-foreground truncate">
                    {project.description}
                  </p>
                </div>
                <div className="flex items-center gap-2 ml-2">
                  <Badge 
                    variant={project.isActive  ? 'success' : 'info'}
                    className="text-xs"
                  >
                    {project.isActive ? '活跃' : '非活跃'}
                  </Badge>
                  <Badge variant="info" className="text-xs">
                    {project.memberCount} 人
                  </Badge>
                </div>
              </div>
               
            </div>
            
            {index < projects.length - 1 && <Separator className="mt-3" />}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
