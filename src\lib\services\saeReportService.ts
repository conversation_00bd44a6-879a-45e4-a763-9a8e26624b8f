import { prisma } from '@/lib/db'
import { v4 as uuidv4 } from 'uuid'
import { ReportType, ReportStatus } from 'generated-prisma'
import type { ExtractedSAEInfo } from './aiExtractor'
import { retry } from '@/lib/decorators/retry'

export interface CreateSAEReportParams {
  saeInfo: ExtractedSAEInfo
  projectId: number
  originalEmailId?: string
  reportType?: ReportType
}

export interface SAEReportResult {
  id: number
  reportUuid: string
  projectId: number
  protocolNumber?: string
  subjectNumber?: string
  eventName?: string
  reportType: ReportType
  status: ReportStatus
  createdAt: Date
}

/**
 * SAE报告服务
 * 负责SAE报告的创建、更新和管理
 */
export class SAEReportService {
  
  /**
   * 创建SAE报告
   */
  @retry()
  async createSAEReport(params: CreateSAEReportParams): Promise<SAEReportResult> {
    const { saeInfo, projectId, originalEmailId, reportType = ReportType.INITIAL } = params
    
    console.log('创建SAE报告:', { projectId, protocolNumber: saeInfo.protocolNumber })
    
    try {
      // 生成唯一的报告UUID
      const reportUuid = uuidv4()
      
      // 解析获知日期
      let learnedDate: Date | null = null
      if (saeInfo.learnedDate) {
        try {
          // 支持多种日期格式
          const dateStr = saeInfo.learnedDate.replace(/[\/\-]/g, '-')
          learnedDate = new Date(dateStr)
          
          // 验证日期有效性
          if (isNaN(learnedDate.getTime())) {
            console.warn('无效的获知日期格式:', saeInfo.learnedDate)
            learnedDate = null
          }
        } catch (error) {
          console.warn('解析获知日期失败:', saeInfo.learnedDate, error)
        }
      }
      
      // 创建SAE报告记录
      const saeReport = await prisma.sAEReport.create({
        data: {
          reportUuid,
          projectId,
          protocolNumber: saeInfo.protocolNumber || null,
          subjectNumber: saeInfo.subjectNumber || null,
          eventName: saeInfo.eventName || null,
          reportType,
          learnedDate,
          seriousness: saeInfo.seriousness || null,
          eventType: saeInfo.eventType || null,
          causality: saeInfo.causality || null,
          originalEmailId: originalEmailId || null,
          status: ReportStatus.RECEIVED
        }
      })
      
      console.log('SAE报告创建成功:', { id: saeReport.id, uuid: reportUuid })
      
      return {
        id: saeReport.id,
        reportUuid: saeReport.reportUuid,
        projectId: saeReport.projectId,
        protocolNumber: saeReport.protocolNumber || undefined,
        subjectNumber: saeReport.subjectNumber || undefined,
        eventName: saeReport.eventName || undefined,
        reportType: saeReport.reportType,
        status: saeReport.status,
        createdAt: saeReport.createdAt
      }
      
    } catch (error) {
      console.error('创建SAE报告失败:', error)
      throw new Error(`创建SAE报告失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 更新报告状态
   */
  @retry()
  async updateReportStatus(reportId: number, status: ReportStatus): Promise<void> {
    try {
      await prisma.sAEReport.update({
        where: { id: reportId },
        data: { 
          status,
          updatedAt: new Date()
        }
      })
      
      console.log('SAE报告状态更新成功:', { reportId, status })
      
    } catch (error) {
      console.error('更新SAE报告状态失败:', error)
      throw new Error(`更新报告状态失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 根据ID获取报告详情
   */
  async getReportById(reportId: number) {
    try {
      const report = await prisma.sAEReport.findUnique({
        where: { id: reportId },
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          },
          attachments: true
        }
      })
      
      if (!report) {
        throw new Error(`报告不存在: ${reportId}`)
      }
      
      return report
      
    } catch (error) {
      console.error('获取SAE报告失败:', error)
      throw new Error(`获取报告失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 根据UUID获取报告详情
   */
  async getReportByUuid(reportUuid: string) {
    try {
      const report = await prisma.sAEReport.findUnique({
        where: { reportUuid },
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          },
          attachments: true
        }
      })
      
      if (!report) {
        throw new Error(`报告不存在: ${reportUuid}`)
      }
      
      return report
      
    } catch (error) {
      console.error('获取SAE报告失败:', error)
      throw new Error(`获取报告失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 检查是否存在重复报告
   */
  async checkDuplicateReport(protocolNumber: string, subjectNumber: string, eventName: string): Promise<boolean> {
    try {
      const existingReport = await prisma.sAEReport.findFirst({
        where: {
          protocolNumber,
          subjectNumber,
          eventName,
          status: {
            not: ReportStatus.COMPLETED
          }
        }
      })
      
      return !!existingReport
      
    } catch (error) {
      console.error('检查重复报告失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const saeReportService = new SAEReportService()
