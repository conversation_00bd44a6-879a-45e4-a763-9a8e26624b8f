import { cn } from '@/lib/utils'

interface TableProps {
  children: React.ReactNode
  className?: string
  stickyHeader?: boolean
}

export function Table({ children, className, stickyHeader = true }: TableProps) {
  return (
    <div className="overflow-x-auto max-h-[70vh] border border-gray-200 rounded-lg">
      <table className={cn('min-w-full divide-y divide-gray-200', className)}>
        {children}
      </table>
    </div>
  )
}

interface TableHeaderProps {
  children: React.ReactNode
  className?: string
  sticky?: boolean
}

export function TableHeader({ children, className, sticky = true }: TableHeaderProps) {
  return (
    <thead className={cn(
      'bg-gray-50',
      sticky && 'sticky top-0 z-10',
      className
    )}>
      {children}
    </thead>
  )
}

interface TableBodyProps {
  children: React.ReactNode
  className?: string
}

export function TableBody({ children, className }: TableBodyProps) {
  return (
    <tbody className={cn('bg-white divide-y divide-gray-200', className)}>
      {children}
    </tbody>
  )
}

interface TableRowProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
}

export function TableRow({ children, className, onClick }: TableRowProps) {
  return (
    <tr
      className={cn(
        'hover:bg-gray-50',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {children}
    </tr>
  )
}

interface TableHeadProps {
  children: React.ReactNode
  className?: string
  sortable?: boolean
  onSort?: () => void
  sticky?: boolean
}

export function TableHead({ children, className, sortable, onSort, sticky }: TableHeadProps) {
  return (
    <th
      className={cn(
        'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50',
        sortable && 'cursor-pointer hover:bg-gray-100',
        sticky && 'sticky top-0 z-10',
        className
      )}
      onClick={sortable ? onSort : undefined}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        {sortable && (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
          </svg>
        )}
      </div>
    </th>
  )
}
interface TableActionHeadProps {
  children: React.ReactNode
  className?: string
}

export function TableActionHead({ children, className }: TableActionHeadProps) {
  return (
    <th
      className={cn(
        'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 sticky top-0 z-10 right-0',
        className
      )}
    >
      {children}
    </th>
  )
}

interface TableActionCellProps {
  children: React.ReactNode
  className?: string
}

export function TableActionCell({ children, className }: TableActionCellProps) {
  return (
    <td className={cn(
      'px-6 py-4 whitespace-nowrap text-sm font-medium sticky right-0 bg-white border-l border-gray-200',
      className
    )}>
      {children}
    </td>
  )
}

interface TableCellProps {
  children: React.ReactNode
  className?: string
}

export function TableCell({ children, className }: TableCellProps) {
  return (
    <td className={cn('px-6 py-4 whitespace-nowrap text-sm text-gray-900', className)}>
      {children}
    </td>
  )
}
