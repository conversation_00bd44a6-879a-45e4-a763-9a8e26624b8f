'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export default function HomePage() {
  const router = useRouter()

  useEffect(() => {
    // 检查是否有认证cookie，如果有则跳转到仪表板，否则跳转到登录页
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/identify', {
          method: 'GET',
          credentials: 'include'
        })

        if (response.ok) {
          router.push('/dashboard')
        } else {
          router.push('/login')
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        router.push('/login')
      }
    }

    checkAuth()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <LoadingSpinner size="lg" />
    </div>
  )
}
