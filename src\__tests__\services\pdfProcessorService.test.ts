import { PDFProcessorService } from '@/lib/services/pdfProcessorService'
import fs from 'fs/promises'
import path from 'path'
 
describe('PDFProcessorService', () => {
  const testPdfPath = path.join(process.cwd(), 'SKB15.pdf')
  let testPdfBuffer: Buffer

  beforeAll(async () => {
    try {
      // 读取测试PDF文件
      testPdfBuffer = await fs.readFile(testPdfPath)
      console.log(`测试PDF文件大小: ${testPdfBuffer.length} bytes`)
    } catch (error) {
      console.error('无法读取测试PDF文件:', error)
      throw new Error('测试PDF文件不存在，请确保 SKB15.pdf 在项目根目录')
    }
  })

  describe('processPdfWithOCR', () => {
    it('应该成功处理PDF并提取文本', async () => {
      const result = await PDFProcessorService.processPdfWithOCR(
        testPdfBuffer,
        'SKB15.pdf',
        {  } // 启用测试优化模式
      )

      // 基本结果验证
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
      
      // 处理时间验证
      expect(result.processingTime).toBeGreaterThan(0)
      console.log(`处理时间: ${result.processingTime}ms`)

      // 页面数量验证
      expect(result.pageCount).toBeGreaterThan(0)
      console.log(`页面数量: ${result.pageCount}`)

      // 文本提取验证
      expect(result.extractedText).toBeDefined()
      expect(result.extractedText!.length).toBeGreaterThan(0)
      console.log(`提取文本长度: ${result.extractedText!.length} 字符`)
      console.log(`文本预览: ${result.extractedText!.substring(0, 200)}...`)
 

      // 签名和完整性检查
      expect(typeof result.hasSignature).toBe('boolean')
      expect(typeof result.isPdfComplete).toBe('boolean')
      expect(typeof result.isPageNumberContinuity).toBe('boolean')
      console.log(`包含签名: ${result.hasSignature}`)
      console.log(`PDF完整: ${result.isPdfComplete}`)
      console.log(`页码完整: ${result.isPageNumberContinuity}`)

      // 页面处理结果验证
      expect(result.pageResults).toBeDefined()
      expect(result.pageResults!.length).toBe(result.pageCount)
      
      const successfulPages = result.pageResults!.filter(page => page.success)
      const failedPages = result.pageResults!.filter(page => !page.success)
      
      console.log(`成功处理页面: ${successfulPages.length}/${result.pageCount}`)
      if (failedPages.length > 0) {
        console.log(`失败页面: ${failedPages.map(p => p.pageNumber).join(', ')}`)
      }

      // 至少应该有一些页面处理成功
      expect(successfulPages.length).toBeGreaterThan(0)


      if( result.extractedText){
        fs.writeFile('extractedText.md', result.extractedText)
      }
    }, 200000) // 10秒超时（Mock后大幅减少）
 
 
  })
  
   
  
})
