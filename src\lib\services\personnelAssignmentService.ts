import { prisma } from '@/lib/db'
import { UserRole, TaskStatus } from 'generated-prisma'

/**
 * 人员分配结果接口
 */
export interface AssignedPersonnel {
  processor: {
    id: number
    name: string
    email: string
    roles: UserRole[]
  } | null
  dataEntry: {
    id: number
    name: string
    email: string
    roles: UserRole[]
  } | null
  qualityControl: {
    id: number
    name: string
    email: string
    roles: UserRole[]
  } | null
}

/**
 * 用户工作量统计接口
 */
export interface UserWorkloadStats {
  date: string
  projectId?: number
  users: Array<{
    user: {
      id: number
      name: string
      email: string
      roles: UserRole[]
    }
    todayStats: {
      processorTasks: number
      dataEntryTasks: number
      qualityControlTasks: number
      totalTasks: number
    }
    limits: {
      dailyMin: number
      dailyMax: number
    }
    status: {
      underMin: boolean
      atCapacity: boolean
      canAcceptMore: boolean
      utilizationRate: number
    }
  }>
}

/**
 * 用户信息接口
 */
interface UserInfo {
  id: number
  name: string
  email: string
  roles: any
  dailyReportMin: number | null
  dailyReportMax: number | null
  isActive: boolean
}

/**
 * 人员分配服务
 * 负责根据用户工作量和项目需求智能分配任务处理人员
 */
export class PersonnelAssignmentService {
  
  /**
   * 分配处理人员
   * @param projectId 项目ID，可选
   * @returns 分配的人员信息
   */
  async assignPersonnel(projectId?: number): Promise<AssignedPersonnel> {
    const result: AssignedPersonnel = {
      processor: null,
      dataEntry: null,
      qualityControl: null
    }

    try {
      if (projectId) {
        // 基于项目分配人员，使用智能分配策略
        const projectUsers = await prisma.userProject.findMany({
          where: {
            projectId: projectId
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                roles: true,
                dailyReportMin: true,
                dailyReportMax: true,
                isActive: true
              }
            }
          }
        })

        // 按角色分组用户
        const usersByRole = {
          processor: projectUsers.filter(up =>
            up.user.isActive && (up.user.roles as UserRole[]).includes(UserRole.Triage)
          ).map(up => up.user),
          dataEntry: projectUsers.filter(up =>
            up.user.isActive && (up.user.roles as UserRole[]).includes(UserRole.PM)
          ).map(up => up.user),
          qualityControl: projectUsers.filter(up =>
            up.user.isActive && (up.user.roles as UserRole[]).includes(UserRole.WM)
          ).map(up => up.user)
        }

        // 智能分配每个角色
        result.processor = await this.assignUserByWorkload(usersByRole.processor, 'assignedToId')
        result.dataEntry = await this.assignUserByWorkload(usersByRole.dataEntry, 'deToId')
        result.qualityControl = await this.assignUserByWorkload(usersByRole.qualityControl, 'qcToId')
      }

      // 如果项目分配失败，使用默认分配策略
      if (!result.processor || !result.dataEntry || !result.qualityControl) {
        const defaultUsers = await this.getDefaultAssignees()

        if (!result.processor) result.processor = defaultUsers.processor
        if (!result.dataEntry) result.dataEntry = defaultUsers.dataEntry
        if (!result.qualityControl) result.qualityControl = defaultUsers.qualityControl
      }

    } catch (error) {
      console.error('人员分配失败:', error)
    }

    return result
  }

  /**
   * 基于工作量智能分配用户
   */
  private async assignUserByWorkload(
    users: UserInfo[],
    assignmentField: 'assignedToId' | 'deToId' | 'qcToId'
  ) {
    if (!users || users.length === 0) {
      return null
    }

    // 获取今日开始时间
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)

    // 获取每个用户今日的任务分配数量
    const userWorkloads = await Promise.all(
      users.map(async (user) => {
        const todayTaskCount = await prisma.task.count({
          where: {
            [assignmentField]: user.id,
            createdAt: {
              gte: todayStart
            },
            status: {
              in: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED]
            }
          }
        })

        return {
          user,
          todayTaskCount,
          dailyMin: user.dailyReportMin || 0,
          dailyMax: user.dailyReportMax || 10,
          canAcceptMore: todayTaskCount < (user.dailyReportMax || 10)
        }
      })
    )

    console.log(`${assignmentField} 用户工作量统计:`, userWorkloads.map(w => ({
      name: w.user.name,
      todayCount: w.todayTaskCount,
      min: w.dailyMin,
      max: w.dailyMax,
      canAcceptMore: w.canAcceptMore
    })))

    // 1. 优先分配给未达到最小任务数的用户
    const underMinUsers = userWorkloads.filter(w => w.todayTaskCount < w.dailyMin && w.canAcceptMore)
    if (underMinUsers.length > 0) {
      // 按今日任务数升序排序，优先分配给任务最少的用户
      underMinUsers.sort((a, b) => a.todayTaskCount - b.todayTaskCount)
      console.log(`分配给未达到最小任务数的用户: ${underMinUsers[0].user.name}`)
      return underMinUsers[0].user
    }

    // 2. 分配给还能接受更多任务的用户（未达到最大值）
    const availableUsers = userWorkloads.filter(w => w.canAcceptMore)
    if (availableUsers.length > 0) {
      // 按今日任务数升序排序，实现轮流分配
      availableUsers.sort((a, b) => a.todayTaskCount - b.todayTaskCount)
      console.log(`轮流分配给可用用户: ${availableUsers[0].user.name}`)
      return availableUsers[0].user
    }

    // 3. 如果所有用户都已达到最大值，选择任务数最少的用户（超出限制的轮流分配）
    userWorkloads.sort((a, b) => a.todayTaskCount - b.todayTaskCount)
    console.log(`所有用户已达上限，轮流分配给: ${userWorkloads[0].user.name}`)
    return userWorkloads[0].user
  }

  /**
   * 获取默认分配人员
   */
  private async getDefaultAssignees(): Promise<AssignedPersonnel> {
    // 使用智能分配策略获取默认用户
    const triageUsers = await prisma.user.findMany({
      where: {
        roles: {
          path: '$',
          array_contains: UserRole.Triage
        },
        isActive: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        roles: true,
        dailyReportMin: true,
        dailyReportMax: true,
        isActive: true
      }
    })

    const pmUsers = await prisma.user.findMany({
      where: {
        roles: {
          path: '$',
          array_contains: UserRole.PM
        },
        isActive: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        roles: true,
        dailyReportMin: true,
        dailyReportMax: true,
        isActive: true
      }
    })

    const wmUsers = await prisma.user.findMany({
      where: {
        roles: {
          path: '$',
          array_contains: UserRole.WM
        },
        isActive: true
      },
      select: {
        id: true,
        name: true,
        email: true,
        roles: true,
        dailyReportMin: true,
        dailyReportMax: true,
        isActive: true
      }
    })

    return {
      processor: await this.assignUserByWorkload(triageUsers, 'assignedToId'),
      dataEntry: await this.assignUserByWorkload(pmUsers, 'deToId'),
      qualityControl: await this.assignUserByWorkload(wmUsers, 'qcToId')
    }
  }

  /**
   * 获取用户工作量统计信息
   * @param projectId 项目ID，可选
   * @returns 用户工作量统计
   */
  async getUserWorkloadStats(projectId?: number): Promise<UserWorkloadStats> {
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)

    // 获取所有活跃用户
    let users
    if (projectId) {
      // 获取项目相关用户
      const projectUsers = await prisma.userProject.findMany({
        where: { projectId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              roles: true,
              dailyReportMin: true,
              dailyReportMax: true,
              isActive: true
            }
          }
        }
      })
      users = projectUsers.filter(up => up.user.isActive).map(up => up.user)
    } else {
      // 获取所有活跃用户
      users = await prisma.user.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          email: true,
          roles: true,
          dailyReportMin: true,
          dailyReportMax: true,
          isActive: true
        }
      })
    }

    // 统计每个用户的工作量
    const workloadStats = await Promise.all(
      users.map(async (user) => {
        const [processorCount, dataEntryCount, qualityControlCount] = await Promise.all([
          prisma.task.count({
            where: {
              assignedToId: user.id,
              createdAt: { gte: todayStart },
              status: { in: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED] }
            }
          }),
          prisma.task.count({
            where: {
              deToId: user.id,
              createdAt: { gte: todayStart },
              status: { in: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED] }
            }
          }),
          prisma.task.count({
            where: {
              qcToId: user.id,
              createdAt: { gte: todayStart },
              status: { in: [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED] }
            }
          })
        ])

        const totalTasks = processorCount + dataEntryCount + qualityControlCount
        const dailyMin = user.dailyReportMin || 0
        const dailyMax = user.dailyReportMax || 10

        return {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            roles: user.roles as UserRole[]
          },
          todayStats: {
            processorTasks: processorCount,
            dataEntryTasks: dataEntryCount,
            qualityControlTasks: qualityControlCount,
            totalTasks
          },
          limits: {
            dailyMin,
            dailyMax
          },
          status: {
            underMin: totalTasks < dailyMin,
            atCapacity: totalTasks >= dailyMax,
            canAcceptMore: totalTasks < dailyMax,
            utilizationRate: dailyMax > 0 ? Math.round((totalTasks / dailyMax) * 100) : 0
          }
        }
      })
    )

    return {
      date: todayStart.toISOString().split('T')[0],
      projectId,
      users: workloadStats.sort((a, b) => b.todayStats.totalTasks - a.todayStats.totalTasks)
    }
  }
}

// 创建单例实例供其他模块使用
export const personnelAssignmentService = new PersonnelAssignmentService()
