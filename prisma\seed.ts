import { PrismaClient } from '../node_modules/generated-prisma'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始数据库种子数据初始化...')

  try {
    // 检查是否已存在管理员用户
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingAdmin) {
      console.log('✅ 管理员用户已存在，跳过创建')
      return
    }

    // 创建默认管理员用户
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '系统管理员',
        domainAccount: 'pharmaron-bj\\52617', // 域账号
        roles: ['PM', 'WM', 'Triage'], // 拥有所有角色权限
        department: 'IT部门',
        isActive: true,
        dailyReportMin: 0,
        dailyReportMax: 100
      }
    })

    console.log('✅ 默认管理员用户创建成功:')
    console.log(`   邮箱: ${adminUser.email}`)
    console.log(`   姓名: ${adminUser.name}`)
    console.log(`   域账号: ${adminUser.domainAccount}`)
    console.log(`   角色: ${JSON.stringify(adminUser.roles)}`)
    console.log(`   部门: ${adminUser.department}`)
 
 
    console.log('🎉 数据库种子数据初始化完成！') 

  } catch (error) {
    console.error('❌ 种子数据初始化失败:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
