import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CheckSquare, Eye, X } from 'lucide-react';

interface EmailSelectorProps {
  selectedEmailsCount: number;
  onCheckEmails: () => void;
  onClearEmails: () => void;
}

export function EmailSelector({
  selectedEmailsCount,
  onCheckEmails,
  onClearEmails
}: EmailSelectorProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <CheckSquare className="h-4 w-4" />
            邮件选择 ({selectedEmailsCount})
          </CardTitle>
          {selectedEmailsCount > 0 && (
            <Button
              onClick={onClearEmails}
              variant="outline"
              size="sm"
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-3 w-3 mr-1" />
              清空
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <Button
          onClick={onCheckEmails}
          variant="outline"
          className="w-full"
        >
          <Eye className="h-4 w-4 mr-2" />
          选择邮件
        </Button>

        {selectedEmailsCount > 0 && (
          <div className="text-xs text-muted-foreground text-center">
            已选择 {selectedEmailsCount} 封邮件
          </div>
        )}
      </CardContent>
    </Card>
  );
}
