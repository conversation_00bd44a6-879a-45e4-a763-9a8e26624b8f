// Outlook插件相关类型定义

export interface UserInfo {
  email: string;
  displayName: string;
  roles: ('WM' | 'Triage' | 'PM')[];
  department?: string;
  dailyReportMin?: number;
  dailyReportMax?: number;
  isLoggedIn: boolean;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  memberCount: number;
}

export interface EmailItem {
  id: string;
  subject: string;
  sender: string;
  receivedTime: string;
  body?: string;
}

export interface AuthState {
  userInfo: UserInfo | null;
  isLoading: boolean;
  isLoggingIn: boolean;
  loginEmail: string;
  loginPassword: string;
}

export interface ProjectState {
  projects: Project[];
  isLoading: boolean;
}

export interface EmailState {
  selectedEmails: any[]; // 使用FullEmailInfo类型，但为了避免循环依赖，这里用any
  isProcessing: boolean;
}

// Office.js 全局类型声明
declare global {
  interface Window {
    Office: any;
  }
}
