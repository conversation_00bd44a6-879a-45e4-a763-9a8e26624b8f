<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0" xmlns:mailappor="http://schemas.microsoft.com/office/mailappversionoverrides/1.0" xsi:type="MailApp">
  <Id>1a67f22b-9785-4d9c-b563-313aeba9c017</Id>
  <Version>*******</Version>
  <ProviderName>pharmaron</ProviderName>
  <DefaultLocale>en-US</DefaultLocale>
  <DisplayName DefaultValue="Triage邮件分发系统"/>
  <Description DefaultValue="邮件自动分发和项目管理系统"/>
  <IconUrl DefaultValue="https://localhost:3100/outlook_res/icon-64.png"/>
  <HighResolutionIconUrl DefaultValue="https://localhost:3100/outlook_res/icon-128.png"/>
  <SupportUrl DefaultValue="https://www.pharmaron.cn/"/>
  <AppDomains>
    <AppDomain>https://www.pharmaron.cn/</AppDomain>
  </AppDomains>
  <Hosts>
    <Host Name="Mailbox"/>
  </Hosts>
  <Requirements>
    <Sets DefaultMinVersion="1.3">
      <Set Name="Mailbox" />
    </Sets>
  </Requirements>
  <FormSettings>
    <Form xsi:type="ItemRead">
      <DesktopSettings>
        <SourceLocation DefaultValue="https://localhost:3100/outlook/taskpane"/>
        <RequestedHeight>400</RequestedHeight>
      </DesktopSettings>
    </Form>
  </FormSettings>
  <Permissions>ReadWriteMailbox</Permissions>
  <Rule xsi:type="RuleCollection" Mode="Or">
    <Rule xsi:type="ItemIs" ItemType="Message" FormType="Read"/>
    <Rule xsi:type="ItemIs" ItemType="Message" FormType="ReadOrEdit"/>
  </Rule>
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/mailappversionoverrides" xsi:type="VersionOverridesV1_0">
    <VersionOverrides xmlns="http://schemas.microsoft.com/office/mailappversionoverrides/1.1" xsi:type="VersionOverridesV1_1">
      <Requirements>
        <bt:Sets DefaultMinVersion="1.5">
          <bt:Set Name="Mailbox"/>
        </bt:Sets>
      </Requirements>
      <Hosts>
        <Host xsi:type="MailHost">
          <DesktopFormFactor>
            <FunctionFile resid="Commands.Url"/>  
             
            <ExtensionPoint xsi:type="MessageReadCommandSurface">
              <OfficeTab id="TabDefault">
                <Group id="msgReadGroup">
                  <Label resid="GroupLabel"/>
                  <Control xsi:type="Button" id="msgReadOpenPaneButton">
                    <Label resid="TaskpaneButton.Label"/>
                    <Supertip>
                      <Title resid="TaskpaneButton.Label"/>
                      <Description resid="TaskpaneButton.Tooltip"/>
                    </Supertip>
                    <Icon>
                      <bt:Image size="16" resid="Icon.16x16"/>
                      <bt:Image size="32" resid="Icon.32x32"/>
                      <bt:Image size="80" resid="Icon.80x80"/>
                    </Icon>
                    <Action xsi:type="ShowTaskpane">
                      <SourceLocation resid="Taskpane.Url"/>
                      <SupportsPinning>true</SupportsPinning>
                      <SupportsNoItemContext>true</SupportsNoItemContext>
                      <!-- Enables your add-in to activate on multiple selected messages. -->
                      <SupportsMultiSelect>true</SupportsMultiSelect>
                    </Action>
                  </Control>
                </Group>
              </OfficeTab>
            </ExtensionPoint>
 
          </DesktopFormFactor>
        </Host>
      </Hosts>
      <Resources>
        <bt:Images>
          <bt:Image id="Icon.16x16" DefaultValue="https://localhost:3100/outlook_res/icon-16.png"/>
          <bt:Image id="Icon.32x32" DefaultValue="https://localhost:3100/outlook_res/icon-32.png"/>
          <bt:Image id="Icon.80x80" DefaultValue="https://localhost:3100/outlook_res/icon-80.png"/>
        </bt:Images>
        <bt:Urls>
          <bt:Url id="Commands.Url" DefaultValue="https://localhost:3100/outlook/commands"/>
          <bt:Url id="Taskpane.Url" DefaultValue="https://localhost:3100/outlook/taskpane"/>
        </bt:Urls>
        <bt:ShortStrings>
          <bt:String id="GroupLabel" DefaultValue="Triage邮件分发"/>
          <bt:String id="TaskpaneButton.Label" DefaultValue="邮件分发面板"/>
          <bt:String id="AutoDistributeButton.Label" DefaultValue="自动识别分发"/>
        </bt:ShortStrings>
        <bt:LongStrings>
          <bt:String id="TaskpaneButton.Tooltip" DefaultValue="打开邮件分发面板，查看用户信息和项目列表"/>
          <bt:String id="AutoDistributeButton.Tooltip" DefaultValue="对选中的邮件进行自动识别和分发处理"/>
        </bt:LongStrings> 
      </Resources>
    </VersionOverrides>
  </VersionOverrides>
</OfficeApp>