// 测试内联图片处理逻辑的单元测试
describe('内联图片处理', () => {
  test('应该将内联图片转换为base64并从附件中移除', () => {
    // 模拟HTML内容
    const originalBody = `
      <html>
        <body>
          <p>这是一封包含内联图片的邮件</p>
          <img src="cid:image1.png" alt="图片1">
          <img src="cid:image2.jpg" alt="图片2">
          <p>还有一些文本</p>
        </body>
      </html>
    `;

    // 模拟附件数据
    const mockAttachments = [
      {
        name: 'image1.png',
        size: 1024,
        isInline: true,
        id: 'att1',
        contentType: 'image/png',
        content: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
        encoding: 'base64'
      },
      {
        name: 'image2.jpg',
        size: 2048,
        isInline: true,
        id: 'att2',
        contentType: 'image/jpeg',
        content: '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA==',
        encoding: 'base64'
      },
      {
        name: 'document.pdf',
        size: 4096,
        isInline: false,
        id: 'att3',
        contentType: 'application/pdf',
        content: 'JVBERi0xLjQKJcOkw7zDtsO8w6...',
        encoding: 'base64'
      }
    ];

    // 模拟处理逻辑
    let body = originalBody;
    const processedInlineIds = new Set<string>();

    const inlineAttachments = mockAttachments.filter(att => att.isInline);

    inlineAttachments.forEach(att => {
      if (att.contentType.startsWith('image/')) {
        const base64DataUrl = `data:${att.contentType};base64,${att.content}`;

        const patterns = [
          new RegExp(`cid:${att.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi'),
          new RegExp(`cid:${att.id.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi'),
          new RegExp(`src=["']${att.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["']`, 'gi')
        ];

        let wasReplaced = false;
        patterns.forEach(pattern => {
          body = body.replace(pattern, (match) => {
            wasReplaced = true;
            if (match.startsWith('cid:')) {
              return base64DataUrl;
            } else {
              return `src="${base64DataUrl}"`;
            }
          });
        });

        if (wasReplaced) {
          processedInlineIds.add(att.id);
        }
      }
    });

    const finalAttachments = mockAttachments.filter(att =>
      !att.isInline || !processedInlineIds.has(att.id)
    );

    // 验证结果
    expect(body).toContain('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==');
    expect(body).toContain('data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA==');
    expect(body).not.toContain('cid:image1.png');
    expect(body).not.toContain('cid:image2.jpg');

    // 验证附件列表只包含PDF文档，内联图片已被移除
    expect(finalAttachments).toHaveLength(1);
    expect(finalAttachments[0].name).toBe('document.pdf');
    expect(finalAttachments[0].isInline).toBe(false);
  });
});