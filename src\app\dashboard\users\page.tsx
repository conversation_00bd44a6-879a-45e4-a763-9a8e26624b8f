'use client'

import { useEffect, useState } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/Button'
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell, TableActionHead, TableActionCell } from '@/components/ui/Table'
import { UserForm } from '@/components/forms/UserForm'
import { formatDate, getRolesText } from '@/lib/utils'

interface User {
  id: number
  email: string
  name: string
  roles: string[]
  department?: string
  domainAccount: string
  isActive: boolean
  createdAt: string
   
}

interface UsersResponse {
  success: boolean
  data: {
    users: User[]
    error?: string;
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [roleFilter, setRoleFilter] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  // 表单状态
  const [showForm, setShowForm] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')

  useEffect(() => {
    loadUsers()
  }, [page, search, roleFilter])

  const loadUsers = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search }),
        ...(roleFilter && { role: roleFilter })
      })

      const response = await fetch(`/api/users?${params}`, {
        credentials: 'include'
      })

      const result: UsersResponse = await response.json()

      if (result.success) {
        setUsers(result.data.users)
        setPagination(result.data.pagination)
      } else {
        setError(result.data?.error || '获取用户列表失败')
      }
    } catch (error) {
      console.error('Load users failed:', error)
      setError('获取用户列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPage(1)
    loadUsers()
  }

  const handleCreateUser = () => {
    setEditingUser(null)
    setFormMode('create')
    setShowForm(true)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setFormMode('edit')
    setShowForm(true)
  }

  const handleToggleUserStatus = async (user: User) => {
    const action = user.isActive ? '停用' : '启用'
    if (!confirm(`确定要${action}用户 "${user.name}" 吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/users`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          id: user.id,
          isActive: !user.isActive
        })
      })

      const result = await response.json()

      if (result.success) {
        loadUsers() // 重新加载列表
      } else {
        setError(result.error || `${action}用户失败`)
      }
    } catch (error) {
      console.error('Toggle user status failed:', error)
      setError(`${action}用户失败`)
    }
  }

  const handleFormSubmit = async (userData: Partial<User>) => {
    try {
      const url = '/api/users'
      const method = formMode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(userData)
      })

      const result = await response.json()

      if (result.success) {
        loadUsers() // 重新加载列表
        setShowForm(false)
      } else {
        throw new Error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('Form submit failed:', error)
      throw error
    }
  }

  if (isLoading && users.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理系统用户和权限分配
          </p>
        </div>
        <Button onClick={handleCreateUser}>
          新建用户
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="搜索用户姓名、邮箱或部门..."
              className="form-input w-full"
            />
          </div>
          <div className="w-48">
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="form-input w-full"
            >
              <option value="">所有角色</option>
              <option value="PM">Project Manager</option>
              <option value="WM">Workflow Manager</option>
              <option value="Triage">Triage</option>
            </select>
          </div>
          <Button type="submit">
            搜索
          </Button>
        </form>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* 用户列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {users.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>用户信息</TableHead>
                <TableHead>角色</TableHead>
                <TableHead>部门</TableHead> 
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableActionHead>操作</TableActionHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {user.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {user.email}
                        </div>
                        <div className="text-xs text-gray-400">
                          域账号: {user.domainAccount || '未配置'}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {user.roles.map(role => (
                        <span key={role} className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {getRolesText(role)}
                        </span>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    {user.department || '-'}
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.isActive ? '活跃' : '停用'}
                    </span>
                  </TableCell>
                  <TableCell>
                    {formatDate(user.createdAt)}
                  </TableCell>
                  <TableActionCell>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditUser(user)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        variant={user.isActive ? "danger" : "success"}
                        onClick={() => handleToggleUserStatus(user)}
                      >
                        {user.isActive ? '停用' : '启用'}
                      </Button>
                    </div>
                  </TableActionCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-8">
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无用户</h3>
            <p className="mt-1 text-sm text-gray-500">
              还没有创建任何用户
            </p>
            <div className="mt-6">
              <Button onClick={handleCreateUser}>
                创建第一个用户
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              上一页
            </button>
            <button
              onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
              disabled={page === pagination.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              下一页
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                显示第 <span className="font-medium">{(page - 1) * pagination.limit + 1}</span> 到{' '}
                <span className="font-medium">
                  {Math.min(page * pagination.limit, pagination.total)}
                </span>{' '}
                条，共 <span className="font-medium">{pagination.total}</span> 条记录
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
                  disabled={page === pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* 用户表单 */}
      <UserForm
        isOpen={showForm}
        onClose={() => setShowForm(false)}
        onSubmit={handleFormSubmit}
        user={editingUser}
        mode={formMode}
      />
    </div>
  )
}
