import { makeAutoObservable, runInAction } from 'mobx';
import { Project } from '../types';
import type { NotificationStore } from './NotificationStore';
import type { AuthStore } from './AuthStore';

export class ProjectStore {
  projects: Project[] = [];
  isLoading = false;

  private notificationStore: NotificationStore;
  private authStore: AuthStore | null = null;

  constructor(notificationStore: NotificationStore) {
    makeAutoObservable(this);
    this.notificationStore = notificationStore;
  }

  // 设置 AuthStore 引用（避免循环依赖）
  setAuthStore = (authStore: AuthStore) => {
    this.authStore = authStore;
  };

  // 加载项目列表
  loadProjects = async () => {
    if (!this.authStore?.userInfo) return;

    this.isLoading = true;
    try {
      const response = await fetch('/api/outlook/projects', {
        credentials: 'include'
      });
      const projectsData = await response.json();
      runInAction(() => {
        if (projectsData.success) {
          this.projects = projectsData.projects || [];
        } else {
          console.error('加载项目列表失败:', projectsData.error);
          this.projects = [];
          this.notificationStore.showError('加载项目列表失败');
        }
      })
    } catch (error) {
      console.error('加载项目列表失败:', error);
      this.projects = [];
      this.notificationStore.showError('加载项目列表失败');
    } finally {
      this.isLoading = false;
    }
  };

  // 清空项目列表
  clearProjects = () => {
    this.projects = [];
  };

  // 根据 ID 获取项目
  getProjectById = (id: string): Project | undefined => {
    return this.projects.find(project => project.id === id);
  };

  // 获取用户有权限的项目
  get availableProjects(): Project[] {
    return this.projects.filter(project => project.isActive !== false);
  }
}
