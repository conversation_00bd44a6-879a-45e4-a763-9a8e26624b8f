'use client'

import { useEffect, useState } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate, getStatusText } from '@/lib/utils'

interface DashboardStats {
  totalReports: number
  pendingReports: number
  processingReports: number
  completedReports: number
  todayReports: number
  avgProcessingTime: number
}

interface RecentReport {
  id: number
  reportUuid: string
  projectId: number
  protocolNumber?: string
  subjectNumber?: string 
  eventName?: string
  status: string
  createdAt: string
  project?: {
    projectCode: string
    projectName: string
  }
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentReports, setRecentReports] = useState<RecentReport[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // 这里应该调用实际的API
      // 暂时使用模拟数据
      setTimeout(() => {
        setStats({
          totalReports: 156,
          pendingReports: 8,
          processingReports: 12,
          completedReports: 136,
          todayReports: 5,
          avgProcessingTime: 2.5
        })

        setRecentReports([
          {
            id: 1,
            reportUuid: 'uuid-1',
            projectId: 1,
            protocolNumber: 'MRG004A-001',
            subjectNumber: '001-001', 
            eventName: '严重不良事件',
            status: 'PROCESSING',
            createdAt: new Date().toISOString(),
            project: {
              projectCode: 'MRG004A-001',
              projectName: 'MRG004A临床试验'
            }
          },
          {
            id: 2,
            reportUuid: 'uuid-2',
            projectId: 1,
            protocolNumber: 'ABC123-002',
            subjectNumber: '002-001', 
            eventName: '药物不良反应',
            status: 'COMPLETED',
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            project: {
              projectCode: 'ABC123-002',
              projectName: 'ABC123临床试验'
            }
          }
        ])

        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const statCards = [
    {
      title: '总报告数',
      value: stats?.totalReports || 0,
      color: 'bg-blue-500'
    },
    {
      title: '待处理',
      value: stats?.pendingReports || 0,
      color: 'bg-yellow-500'
    },
    {
      title: '处理中',
      value: stats?.processingReports || 0,
      color: 'bg-orange-500'
    },
    {
      title: '已完成',
      value: stats?.completedReports || 0,
      color: 'bg-green-500'
    }
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <p className="mt-1 text-sm text-gray-500">
          SAE报告处理概览和最新动态
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card) => (
          <div key={card.title} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-md ${card.color} flex items-center justify-center`}>
                    <span className="text-white text-sm">📊</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {card.title}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {card.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 今日统计 */}
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">今日统计</h3>
            <div className="mt-5 space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">今日新增报告</span>
                <span className="text-sm font-medium text-gray-900">
                  {stats?.todayReports || 0} 个
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">平均处理时间</span>
                <span className="text-sm font-medium text-gray-900">
                  {stats?.avgProcessingTime || 0} 小时
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">处理效率</h3>
            <div className="mt-5 space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">完成率</span>
                <span className="text-sm font-medium text-gray-900">
                  {stats ? Math.round((stats.completedReports / stats.totalReports) * 100) : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ 
                    width: stats ? `${(stats.completedReports / stats.totalReports) * 100}%` : '0%' 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 最近报告 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">最近报告</h3>
        </div>
        <div className="border-t border-gray-200">
          {recentReports.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">方案编号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中心编号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">受试者编号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">事件名称</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recentReports.map((report) => (
                    <tr key={report.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {report.project?.projectCode}
                          </div>
                          <div className="text-sm text-gray-500">
                            {report.project?.projectName}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {report.protocolNumber || '-'}
                      </td> 
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {report.subjectNumber || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {report.eventName || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          {getStatusText(report.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(report.createdAt)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无报告</h3>
              <p className="mt-1 text-sm text-gray-500">
                还没有SAE报告记录
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
