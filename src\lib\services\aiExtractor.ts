import { LLM } from '@/lib/llm'  
import { jsonrepair } from 'jsonrepair'
import { retry } from '@/lib/decorators/retry'

const MODEL_NAME =  process.env.MODEL_NAME || 'dummy-key-for-build'

export interface ExtractedSAEInfo {
  protocolNumber: string 
  learnedDate: string
  centerName: string
  subjectNumber: string
  eventName: string
  seriousness: string
  eventType: string
  causality: string
}

export interface ExtractionResult {
  success: boolean
  data: ExtractedSAEInfo
  confidence: number
  error?: string
  processingTime?: number
  extractedText?: string
}

export interface EmailClassificationResult {
  success: boolean
  data?: {
    taskType: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
    suggestedProject?: string
    projectConfidence?: number
    keywords: string[]
    category: string
    requiresAttention: boolean
    estimatedProcessingTime?: number
  }
  confidence?: number
  error?: string
  processingTime?: number
}

export interface ProjectMatchResult {
  projectId?: number
  projectCode?: string
  confidence: number
  matchReasons: string[]
}



export class AIExtractor {
  /**
   * 从文本中提取SAE关键信息
   */
  async extractSAEInfo(text: string): Promise<ExtractionResult> {
    const startTime = Date.now()
    
    console.log('Starting SAE information extraction...')
    
    const prompt = `
    请从以下SAE报告文本中提取关键信息，并以JSON格式返回：

    需要提取的信息点：
      方案编号 (protocolNumber) 
      研究者获知日期 (learnedDate) - 格式：YYYY/MM/DD
      受试者编号 (subjectNumber)
      中心名称 (centerName)
      SAE事件名称 (eventName)
      SAE严重性标准 (seriousness)
      SAE事件类型 (eventType)
      SAE因果关系 (causality)  

    返回格式示例：
    {
      "protocolNumber": "MRG004A-001", 
      "learnedDate": "2024-01-15",
      "centerName": "中心名称",
      "subjectNumber": "受试者ID = 中心号/受试者筛选号",
      "eventName": "严重不良事件",
      "seriousness": "危及生命",
      "eventType": "SAE / AESI",
      "causality": "与xxx的因果关系,与xxx的因果关系,可能有多个",  
    }

    如果某个信息点无法确定，请设置为null。请确保日期格式正确，布尔值准确。

    文本内容：
    \`\`\`markdown
    ${text}
    \`\`\`
    `
    
    const response = await LLM.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: '你是一个专业的SAE报告信息提取专家。请从提供的文本中准确提取关键信息，并以JSON格式返回。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],         
      tools: [
        {
          type: "function",
          function: {
            name: "extract_sae_info",
            description: "从SAE报告文本中提取关键信息",
            parameters: {
              type: "object",
              properties: {
                protocolNumber: {
                  type: "string",
                  description: "方案编号"
                },
                learnedDate: {
                  type: "string",
                  description: "研究者获知日期，格式：YYYY-MM-DD"
                },
                centerName: {
                  type: "string",
                  description: "中心名称"
                },
                subjectNumber: {
                  type: "string",
                  description: "受试者ID = 中心号/受试者筛选号"
                },
                eventName: {
                  type: "string",
                  description: "SAE事件名称"
                },
                seriousness: {
                  type: "string",
                  description: "SAE严重性标准"
                },
                eventType: {
                  type: "string",
                  description: "SAE事件类型"
                },
                causality: {
                  type: "string",
                  description: "SAE因果关系"
                }
              },
              required: []
            }
          }
        }
      ],
      tool_choice: {
        type: "function",
        function: { name: "extract_sae_info" }
      },
    })
    
    const toolCall = response.choices?.[0]?.message?.tool_calls?.[0];
    if(!toolCall || !toolCall.function || toolCall.function.name !== "extract_sae_info") {
      throw new Error('强制函数调用失败：未收到预期的extract_sae_info响应')
    }
    const { arguments: args } = toolCall.function;
    const result = JSON.parse(jsonrepair(args))
    
    // 验证和标准化提取的数据
    const extractedData = this.validateAndNormalizeData(result)
    
    // 计算置信度
    const confidence = this.calculateConfidence(extractedData, text)
    
    const processingTime = Date.now() - startTime
    
    console.log(`SAE information extraction completed, confidence: ${confidence}, time: ${processingTime}ms`)
    
    return {
      success: true,
      data: extractedData,
      confidence,
      processingTime,
      extractedText: text
    } 
  }

  @retry()
  async findMainAttachment(    
    subject: string,
    body: string,
    sender: string,
    attachments: string[] = []
  ) { 
    const startTime = Date.now() 
    const prompt = `
    请分析以下邮件内容，识别附件列表中哪个才是SAE报告附件：

邮件主题: ${subject}
发件人: ${sender}
邮件正文: ${body}
附件列表: ${attachments.join(', ')}
`

    const response = await LLM.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "sae_attachment",
            description: "附件列表中的SAE报告附件文件名",
            parameters: {
              type: "object",
              properties: {
               attachment_name : { type: "string" },
                
              },
              required: ["attachment_name" ]
            }
          }
        }
      ],
      tool_choice: {
        type: "function",
        function: { name: "sae_attachment" }
      }, 
    })
    const toolCall = response.choices?.[0]?.message?.tool_calls?.[0];
    if(!toolCall || !toolCall.function || toolCall.function.name !== "sae_attachment") {
      throw new Error('强制函数调用失败：未收到预期的sae_attachment响应')
    }
    const { arguments: args } = toolCall.function; 
    const extractedData = JSON.parse(jsonrepair(args));  

    const processingTime = Date.now() - startTime

    return {
      success: true,
      saeAttachmentName: extractedData.attachment_name, 
      processingTime
    } 
  }
 

  // 修改返回类型接口定义
  @retry()
  async extractTextFromImage(
    imageBase64: string
  ): Promise<{
    text: string;
    pageNumber: number | null;
    hasSignature: boolean; 
  }> { 
    // 验证输入参数
    if (!imageBase64 || imageBase64.length < 100) {
      throw new Error('图片数据格式无效或过小')
    }
 
 

    const response = await LLM.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: `你是一个专业的OCR文本提取专家，专注于从文档、表格和图片中提取准确的文本信息。

任务要求：
1. 完整性：提取图片中的所有可读文本，包括标题、正文、表格内容、数值、日期等,对于**所有可选项（包括复选框和单选按钮）**，必须按特定的符号规则进行转换，而不是生成HTML input标签
2. 格式保持：尽可能保持原始格式，特别是表格、列表、换行符
3. 签名识别：仔细检查是否有手写签名或印章
4. 页面识别：仔细检查图片中包含的页码
5. 使用markdown格式: 结构、布局和图片中保持一致`
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: '从图片中提取完整的文本内容'
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/png;base64,${imageBase64}`,
                detail: 'high'
              }
            }
          ]
        }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "extract_image_text",
            description: "从图片中提取结构化文本信息，包括内容和特殊元素检测",
            parameters: {
              type: "object",
              properties: {
                text: { type: "string" },
                hasSignature: {
                  type: 'boolean',
                  description: '图片中是否检测到签名、印章或手写文字'
                },
                pageNumber: {
                  type: 'number',
                  description: '图片中包含的页码，如果无法确定，请返回null'
                },
              },
              required: ["text","hasSignature","pageNumber"]
            }
          }
        }
      ],
      tool_choice: {
        type: "function",
        function: { name: "extract_image_text" }
      }, 
    }) 
    const toolCall = response.choices?.[0]?.message?.tool_calls?.[0];
    if(!toolCall || !toolCall.function || toolCall.function.name !== "extract_image_text") {
      throw new Error('强制函数调用失败：未收到预期的extract_image_text响应')
    }
    const { arguments: args } = toolCall.function; 
    console.log('extract_image_text args:', args)
    const extractedData =  JSON.parse(jsonrepair(args));  

    return {
      text: extractedData.text || '',
      pageNumber: extractedData.pageNumber || null,
      hasSignature: Boolean(extractedData.hasSignature)
    } 
  }
 
 
  /**
   * 验证和标准化提取的数据
   */
  private validateAndNormalizeData(data: any): ExtractedSAEInfo {
    const normalized: any = {}
    
    // 验证和标准化各个字段
    if (data.protocolNumber && typeof data.protocolNumber === 'string') {
      normalized.protocolNumber = data.protocolNumber.trim()
    }
    
    if (data.learnedDate) {
      const date = new Date(data.learnedDate)
      if (!isNaN(date.getTime())) {
        normalized.learnedDate = date.toISOString().split('T')[0]
      }
    }
    
    if (data.centerName && typeof data.centerName === 'string') {
      normalized.centerName = data.centerName.trim()
    } 
    
    if (data.subjectNumber && typeof data.subjectNumber === 'string') {
      normalized.subjectNumber = data.subjectNumber.trim()
    } 
    if (data.eventName && typeof data.eventName === 'string') {
      normalized.eventName = data.eventName.trim()
    }
    
    if (data.seriousness && typeof data.seriousness === 'string') {
      normalized.seriousness = data.seriousness.trim()
    }
    
    if (data.eventType && typeof data.eventType === 'string') {
      normalized.eventType = data.eventType.trim()
    }
    
    if (data.causality && typeof data.causality === 'string') {
      normalized.causality = data.causality.trim()
    } 
    return normalized as ExtractedSAEInfo
  }

  /**
   * 计算提取置信度
   */
  private calculateConfidence(data: ExtractedSAEInfo, originalText: string): number {
    let score = 0
    let totalFields = 0
    
    const fields = [
      'protocolNumber', 'learnedDate', 'subjectNumber' , 'eventName','centerName',
      'seriousness', 'eventType', 'causality'
    ]
    
    for (const field of fields) {
      totalFields++
      if (data[field as keyof ExtractedSAEInfo]) {
        score++
        
        // 如果在原文中能找到相关内容，增加置信度
        const value = data[field as keyof ExtractedSAEInfo] as string
        if (value && originalText.toLowerCase().includes(value.toLowerCase())) {
          score += 0.5
        }
      }
    }
     
    
    const calculatedConfidence = totalFields > 0 ? score / (totalFields * 1.5) : 0
     
    
    return Math.max(0, Math.min(1, calculatedConfidence))
  }
}
