'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>Footer } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface User {
  id?: number
  email: string
  name: string
  roles: string[]
  department?: string
  domainAccount: string
  dailyReportMin?: number
  dailyReportMax?: number
  isActive?: boolean
}

interface UserFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (user: Partial<User>) => Promise<void>
  user?: User | null
  mode: 'create' | 'edit'
}

const roleOptions = [
  { value: 'PM', label: 'Project Manager' },
  { value: 'WM', label: 'Workflow Manager' },
  { value: 'Triage', label: 'Triage' }
]



export function UserForm({ isOpen, onClose, onSubmit, user, mode }: UserFormProps) {
  const [formData, setFormData] = useState<Partial<User>>({
    email: '',
    name: '',
    roles: ['Triage'],
    department: '',
    domainAccount: '',
    dailyReportMin: 0,
    dailyReportMax: 10,
    isActive: true
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (user && mode === 'edit') {
      setFormData({
        id: user.id,
        email: user.email,
        name: user.name,
        roles: user.roles,
        department: user.department || '',
        domainAccount: user.domainAccount || '',
        dailyReportMin: user.dailyReportMin || 0,
        dailyReportMax: user.dailyReportMax || 10,
        isActive: user.isActive
      })

    } else {
      setFormData({
        email: '',
        name: '',
        roles: ['Triage'],
        department: '',
        domainAccount: '',
        dailyReportMin: 0,
        dailyReportMax: 10,
        isActive: true
      })

    }
    setErrors({})
  }, [user, mode, isOpen])



  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.email?.trim()) {
      newErrors.email = '邮箱不能为空'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址'
    }

    if (!formData.name?.trim()) {
      newErrors.name = '姓名不能为空'
    }

    if (!formData.domainAccount?.trim()) {
      newErrors.domainAccount = '域账号不能为空'
    }

    if (!formData.roles || formData.roles.length === 0) {
      newErrors.roles = '请至少选择一个角色'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      await onSubmit({
        ...formData,
        dailyReportMin: formData.dailyReportMin || 0,
        dailyReportMax: formData.dailyReportMax || 10
      })
      onClose()
    } catch (error) {
      console.error('Submit failed:', error)
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? '新建用户' : '编辑用户'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="邮箱 *"
            type="email"
            value={formData.email || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            error={errors.email}
            placeholder="例如：<EMAIL>"
            disabled={mode === 'edit'} // 编辑时不允许修改邮箱
          />
          
          <Input
            label="姓名 *"
            value={formData.name || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            error={errors.name}
            placeholder="例如：张三"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="域账号 *"
            value={formData.domainAccount || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, domainAccount: e.target.value }))}
            error={errors.domainAccount}
            placeholder="例如：zhangsan"
            helperText="用于Exchange邮箱验证的域账号"
          />

          <Input
            label="部门"
            value={formData.department || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
            placeholder="例如：药物警戒部"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              角色 *
            </label>
            <div className="space-y-2">
              {roleOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.roles?.includes(option.value) || false}
                    onChange={(e) => {
                      const currentRoles = formData.roles || []
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          roles: [...currentRoles, option.value]
                        }))
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          roles: currentRoles.filter(role => role !== option.value)
                        }))
                      }
                    }}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-900">{option.label}</span>
                </label>
              ))}
            </div>
            {errors.roles && (
              <p className="text-sm text-red-600 mt-1">{errors.roles}</p>
            )}
          </div>
        </div>

        {/* 用户状态和报告数量设置 */}
        <div className="space-y-4">
          {mode === 'edit' && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive || false}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                用户状态：活跃
              </label>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="每日最少处理报告数"
              type="number"
              min="0"
              value={formData.dailyReportMin || 0}
              onChange={(e) => setFormData(prev => ({ ...prev, dailyReportMin: parseInt(e.target.value) || 0 }))}
              helperText="用户每日最少处理的报告数量"
            />

            <Input
              label="每日最多处理报告数"
              type="number"
              min="0"
              value={formData.dailyReportMax || 10}
              onChange={(e) => setFormData(prev => ({ ...prev, dailyReportMax: parseInt(e.target.value) || 10 }))}
              helperText="用户每日最多处理的报告数量"
            />
          </div>
        </div>

        <ModalFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
          >
            {mode === 'create' ? '创建用户' : '保存更改'}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  )
}
