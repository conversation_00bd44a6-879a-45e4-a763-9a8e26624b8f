import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getCurrentUser } from '@/lib/auth';
import { TaskStatus, TaskPriority } from 'generated-prisma';

export async function GET(request: NextRequest) {
  try {
    // 从cookie获取当前用户
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') as TaskStatus | null;
    const priority = searchParams.get('priority') as TaskPriority | null;
    const projectId = searchParams.get('projectId');
    const assignedToMe = searchParams.get('assignedToMe') === 'true';
    const createdByMe = searchParams.get('createdByMe') === 'true';

    // 构建查询条件
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (priority) {
      where.priority = priority;
    }

    if (projectId) {
      where.projectId = parseInt(projectId);
    }

    if (assignedToMe) {
      where.OR = [
        { assignedToId: user.id },
        { qcToId: user.id },
        { deToId: user.id }
      ];
    }

    if (createdByMe) {
      where.createdById = user.id;
    }

    // 如果没有特定筛选条件，显示与用户相关的任务
    if (!assignedToMe && !createdByMe && !projectId) {
      where.OR = [
        { createdById: user.id },
        { assignedToId: user.id },
        { qcToId: user.id },
        { deToId: user.id }
      ];
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询任务
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          qcTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          deTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.task.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      tasks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取任务列表失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { taskId, status, assignedToId, qcToId, deToId, notes } = body;

    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID不能为空' },
        { status: 400 }
      );
    }

    // 从cookie获取当前用户
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 构建更新数据
    const updateData: any = {
      updatedAt: new Date()
    };

    if (status) {
      updateData.status = status;
      if (status === 'COMPLETED') {
        updateData.completedAt = new Date();
      }
    }

    if (assignedToId !== undefined) {
      updateData.assignedToId = assignedToId;
    }

    if (qcToId !== undefined) {
      updateData.qcToId = qcToId;
    }

    if (deToId !== undefined) {
      updateData.deToId = deToId;
    }

    // 更新任务
    const updatedTask = await prisma.task.update({
      where: { id: parseInt(taskId) },
      data: updateData,
      include: {
        project: {
          select: {
            id: true,
            projectCode: true,
            projectName: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        qcTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        deTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      task: updatedTask
    });

  } catch (error) {
    console.error('更新任务失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
