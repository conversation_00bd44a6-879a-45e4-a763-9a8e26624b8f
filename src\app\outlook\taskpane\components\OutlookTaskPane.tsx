'use client';

import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';

// MobX Stores
import {
  useAuthStore,
  useOfficeStore,
  useProjectStore,
  useEmailStore
} from '../stores';

// Components
import { LoginForm } from './LoginForm';
import { UserInfoCard } from './UserInfoCard';
import { RoleTabs } from './RoleTabs';
import { LoadingSpinner } from './LoadingSpinner';
import { ToastContainer } from './ToastContainer';
import { ConfirmDialogContainer } from './ConfirmDialogContainer';

const OutlookTaskPane: React.FC = observer(() => {
  // MobX Stores
  const authStore = useAuthStore();
  const officeStore = useOfficeStore();
  const projectStore = useProjectStore();
  const emailStore = useEmailStore();
 

  // 初始化效果 - 只在 Office.js 准备好时执行一次
  useEffect(() => {
    if (officeStore.isOfficeReady) {
      // 检查认证状态
      authStore.checkAuthStatus();
    }
  }, [officeStore.isOfficeReady, authStore]);


  // 加载状态
  if (officeStore.isLoading) {
    return <LoadingSpinner message="正在初始化 Office.js..." />;
  }

  // 未登录状态
  if (!authStore.userInfo) {
    return (
      <>
        <LoginForm
          loginEmail={authStore.loginEmail}
          loginPassword={authStore.loginPassword}
          isLoggingIn={authStore.isLoggingIn}
          onPasswordChange={authStore.setLoginPassword}
          onLogin={authStore.handleLogin}
        />
        <ToastContainer />
        <ConfirmDialogContainer />
      </>
    );
  }

  // 已登录状态
  return (
    <>
      <div className="p-4 space-y-4 h-full overflow-y-auto">
        {/* 用户信息卡片 */}
        <UserInfoCard
          userInfo={authStore.userInfo}
          onLogout={authStore.handleLogout}
        />

        {/* 角色功能面板 */}
        <RoleTabs
          userInfo={authStore.userInfo}
          projects={projectStore.projects}
          selectedEmails={emailStore.selectedEmails}
          selectedEmailsCount={emailStore.selectedEmailsCount} 
          onCheckEmails={emailStore.checkSelectedEmails}
          onClearEmails={emailStore.handleClearAllEmails}
          onRemoveEmail={emailStore.removeEmail}
          onDistribute={emailStore.handleDistribute}
          onProcessEmails={emailStore.handleProcessEmails}
        />
      </div>

      {/* Toast 通知容器 */}
      <ToastContainer />

      {/* 确认对话框容器 */}
      <ConfirmDialogContainer />
    </>
  );
});

OutlookTaskPane.displayName = 'OutlookTaskPane';

export default OutlookTaskPane;
