// Jest setup file
import 'dotenv/config'
 

// 增加Jest超时时间
jest.setTimeout(180000) // 3分钟

// 全局测试配置
global.console = {
  ...console,
  // 在测试中保留console.log以便调试
  log: jest.fn((...args) => {
    console.info(...args)
  }),
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
}
 
const mockLangfuse = jest.fn(function mockLangfuseConstructor(t) {
  t.trace = jest.fn(() => ({
    id: 'test-trace-id',
    span: jest.fn(),
  }));
});

jest.mock('langfuse', () => ({
  __esModule: true,
  Langfuse: mockLangfuse,
  observeOpenAI: (openaiInstance ) => openaiInstance,
}));