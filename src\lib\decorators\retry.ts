/**
 * 重试装饰器配置选项
 */
export interface RetryOptions {
  /** 最大重试次数，默认5次 */
  maxRetries?: number
  /** 重试间隔（毫秒），默认1000ms */
  delay?: number
  /** 判断是否应该重试的函数，默认所有错误都重试 */
  shouldRetry?: (error: any, attempt: number) => boolean
  /** 重试时的回调函数 */
  onRetry?: (error: any, attempt: number, maxRetries: number, methodName: string) => void
  /** 最终失败时的回调函数 */
  onFinalFailure?: (error: any, maxRetries: number, methodName: string) => void
  /** 成功时的回调函数 */
  onSuccess?: (result: any, attempt: number, methodName: string) => void
  /** 是否启用详细日志，默认true */
  enableLogging?: boolean
}

/**
 * 延时工具函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 默认的重试判断函数 - 所有错误都重试
 */
const defaultShouldRetry = (error: any, attempt: number): boolean => true

/**
 * 默认的重试回调函数
 */
const defaultOnRetry = (error: any, attempt: number, maxRetries: number, methodName: string): void => {
  console.error(`❌ ${methodName} 失败 (第${attempt}次尝试):`, error)
  if (attempt < maxRetries) {
    console.log(`⏳ 1秒后进行第${attempt + 1}次重试...`)
  }
}

/**
 * 默认的最终失败回调函数
 */
const defaultOnFinalFailure = (error: any, maxRetries: number, methodName: string): void => {
  console.error(`💥 ${methodName} 最终失败，已尝试${maxRetries}次`)
}

/**
 * 默认的成功回调函数
 */
const defaultOnSuccess = (result: any, attempt: number, methodName: string): void => {
  const attemptText = attempt === 1 ? '' : ` (第${attempt}次尝试)`
  console.log(`✅ ${methodName} 成功${attemptText}`)
}

/**
 * 重试装饰器
 * 
 * @param options 重试配置选项
 * @returns 装饰器函数
 * 
 * @example
 * ```typescript
 * class MyService {
 *   @retry()
 *   async defaultRetry() {
 *     // 使用默认配置：5次重试，1秒间隔
 *   }
 * 
 *   @retry({ maxRetries: 3, delay: 500 })
 *   async customRetry() {
 *     // 自定义配置：3次重试，0.5秒间隔
 *   }
 * 
 *   @retry({
 *     maxRetries: 3,
 *     shouldRetry: (error) => error.code !== 'AUTH_ERROR'
 *   })
 *   async conditionalRetry() {
 *     // 条件重试：认证错误不重试
 *   }
 * }
 * ```
 */
export function retry(options: RetryOptions = {}): MethodDecorator {
  const {
    maxRetries = 5,
    delay: retryDelay = 1000,
    shouldRetry = defaultShouldRetry,
    onRetry = defaultOnRetry,
    onFinalFailure = defaultOnFinalFailure,
    onSuccess = defaultOnSuccess,
    enableLogging = true
  } = options

  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const methodName = String(propertyKey)

    if (typeof originalMethod !== 'function') {
      throw new Error(`@retry can only be applied to methods, but ${methodName} is not a function`)
    }

    descriptor.value = async function (...args: any[]) {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          if (enableLogging && attempt > 1) {
            console.log(`🔄 ${methodName} 尝试 ${attempt}/${maxRetries}`)
          } else if (enableLogging && attempt === 1) {
            console.log(`🔄 ${methodName} 开始执行`)
          }

          const result = await originalMethod.apply(this, args)
          
          if (enableLogging) {
            onSuccess(result, attempt, methodName)
          }
          
          return result

        } catch (error) {
          // 检查是否应该重试
          if (!shouldRetry(error, attempt)) {
            if (enableLogging) {
              console.error(`🚫 ${methodName} 错误不可重试:`, error)
            }
            throw error
          }

          // 如果不是最后一次尝试，进行重试
          if (attempt < maxRetries) {
            if (enableLogging) {
              onRetry(error, attempt, maxRetries, methodName)
            }
            await delay(retryDelay)
          } else {
            // 最后一次尝试失败
            if (enableLogging) {
              onFinalFailure(error, maxRetries, methodName)
            }
            throw error
          }
        }
      }

      // 理论上不会到达这里，但为了类型安全
      throw new Error(`${methodName} 重试逻辑异常`)
    }

    return descriptor
  }
}

/**
 * EWS服务专用的重试配置
 */
export const EWS_RETRY_CONFIG: RetryOptions = {
  maxRetries: 3,
  delay: 1000,
  shouldRetry: (error: any, attempt: number) => {
    // 认证错误不重试
    if (error?.message?.includes('Unauthorized') || error?.message?.includes('Authentication')) {
      return false
    }
    // 其他错误都重试
    return true
  },
  onRetry: (error: any, attempt: number, maxRetries: number, methodName: string) => {
    console.error(`❌ EWS ${methodName} 失败 (第${attempt}次尝试):`, error)
    if (attempt < maxRetries) {
      console.log(`⏳ 1秒后进行第${attempt + 1}次重试...`)
    }
  },
  onFinalFailure: (error: any, maxRetries: number, methodName: string) => {
    console.error(`💥 EWS ${methodName} 最终失败，已尝试${maxRetries}次`)
  },
  onSuccess: (result: any, attempt: number, methodName: string) => {
    const attemptText = attempt === 1 ? '' : ` (第${attempt}次尝试)`
    console.log(`✅ EWS ${methodName} 成功${attemptText}`)
  }
}
