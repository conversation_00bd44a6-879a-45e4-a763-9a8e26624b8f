import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser, identifyUserByEmail, generateToken, setAuthCookie } from '@/lib/auth'
import { taskScheduler } from '@/lib/jobs/taskScheduler'

 

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      data: { user }
    })
  } catch (error) {
    console.error('Auth identify GET failed:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      )
    }

    // 通过邮箱识别用户
    const user = await identifyUserByEmail(email)
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found or inactive' },
        { status: 401 }
      )
    }

    // 生成JWT token
    const token = await generateToken(user)
    
    // 设置cookie
    const response = NextResponse.json({
      success: true,
      data: { user, token }
    })

    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7天
      path: '/'
    })

    return response
  } catch (error) {
    console.error('Auth identify POST failed:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
