import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getCurrentUser } from '@/lib/auth';
import { TaskEmailData } from '@/types/task';
import { emlFileService } from '@/lib/services/emlFileService';



interface DistributeEmailsRequest {
  emails: TaskEmailData[]; 
}

export async function POST(request: NextRequest) {
  try {
    const body: DistributeEmailsRequest = await request.json();
    const { emails } = body;

    if (!emails || emails.length === 0) {
      return NextResponse.json(
        { error: '没有要分发的邮件' },
        { status: 400 }
      );
    }

    // 从cookie获取当前用户
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 验证用户权限 - 只有WM用户可以分发
    if (!user.roles.includes('WM')) {
      return NextResponse.json(
        { error: '只有WM用户可以使用分发功能' },
        { status: 403 }
      );
    }
 
    const processedEmails = []; 

    // 处理每封邮件
    for (const email of emails) {
      try {
        // 验证邮件数据完整性
        if (!email.from || !email.body) {
          throw new Error('邮件数据不完整，缺少必要的from或body字段');
        }

        // 构建邮件详情对象
        const emailDetails = {
          from: email.from,
          to: email.to || '',
          cc: email.cc || '',
          subject: email.subject,
          date: email.date || new Date().toUTCString(),
          body: email.body,
          attachments: email.attachments || []
        };

        // 生成EML文件
        const emlFileInfo = await emlFileService.generateEmlFromEmailDetails(emailDetails, email.itemId);

        // 创建任务记录
        const task = await prisma.task.create({
          data: {
            title: `${email.subject}`,
            description: `来自 ${email.sender} 的邮件`,
            status: 'PENDING',
            priority: 'MEDIUM',
            createdById: user.id,
            emlFile: emlFileInfo.filePath,
            mailId: email.itemId,
          },
        });

        // 保存附件到TaskAttachment表
        if (email.attachments && email.attachments.length > 0) {
          await emlFileService.saveTaskAttachments(task.id, email.attachments);
        }

        processedEmails.push({
          emailId: email.itemId,
          taskId: task.id,
          status: 'success',
          emlFile: emlFileInfo.fileName,
        });

      } catch (emailError) {
        console.error(`处理邮件 ${email.itemId} 失败:`, emailError);
        processedEmails.push({
          emailId: email.itemId,
          error: emailError instanceof Error ? emailError.message : '处理失败',
          status: 'error',
        });
      }
    }
 

    return NextResponse.json({
      success: true, 
      totalCount: emails.length,
      results: processedEmails, 
    });
  } catch (error) {
    console.error('邮件分发失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
