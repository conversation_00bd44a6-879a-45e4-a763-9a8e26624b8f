import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { UserRole } from 'generated-prisma'
import { validateEWSCredentials, setAuthCookie, generateToken } from '@/lib/auth'; 

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: '邮箱地址不能为空' },
        { status: 400 }
      );
    }

    if (!password) {
      return NextResponse.json(
        { error: '密码不能为空' },
        { status: 400 }
      );
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        password: true,
        domainAccount: true,
        roles: true,
        department: true,
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        { error: '账户已被禁用，请联系管理员' },
        { status: 403 }
      );
    }

    // 检查用户是否配置了域账号
    if (!user.domainAccount) {
      return NextResponse.json(
        { error: '用户未配置域账号，请联系管理员' },
        { status: 400 }
      );
    }
    // 通过 EWS API 验证域账号和密码
    const isEWSValid = await validateEWSCredentials(user.domainAccount, password);
    if (!isEWSValid) {
      return NextResponse.json(
        { error: '域账号或密码错误' },
        { status: 401 }
      );
    }

    // 更新用户密码（如果需要）
    if (!user.password || user.password !== password) {
      await prisma.user.update({
        where: { email },
        data: { password: password }, // 明文保存密码
      });
    }

    // 返回用户信息（不包含密码）
    const userInfo = {
      id: user.id,
      email: user.email,
      displayName: user.name,
      name: user.name,
      roles: user.roles as UserRole[],
      department: user.department || '',
      isLoggedIn: true,
    };

    await setAuthCookie(await generateToken(userInfo))

    return NextResponse.json({
      success: true,
      user: userInfo,
    });

  } catch (error) {
    console.error('登录失败:', error);
    return NextResponse.json(
      { error: '登录过程中发生错误' },
      { status: 500 }
    );
  }
}
