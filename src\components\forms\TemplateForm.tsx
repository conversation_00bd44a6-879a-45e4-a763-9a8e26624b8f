'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Mo<PERSON>Footer } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface EmailTemplate {
  id?: number
  templateName: string
  templateType: string
  subjectTemplate?: string
  bodyTemplate: string
  recipientEmails?: string[]
  ccEmails?: string[]
  isDefault?: boolean
  projectId?: number
  project?: {
    id: number
    projectCode: string
    projectName: string
  }
}

interface Project {
  id: number
  projectCode: string
  projectName: string
}

interface TemplateFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (template: Partial<EmailTemplate>) => Promise<void>
  template?: EmailTemplate | null
  mode: 'create' | 'edit'
}

const templateTypeOptions = [
  { value: 'CONFIRMATION', label: '确认回复' },
  { value: 'SAFETY_NOTIFICATION', label: '安全性通知' },
  { value: 'INTERNAL_FORWARD', label: '内部转发' },
  { value: 'WM_FORWARD', label: 'WM转发' }
]

export function TemplateForm({ isOpen, onClose, onSubmit, template, mode }: TemplateFormProps) {
  const [formData, setFormData] = useState<Partial<EmailTemplate>>({
    templateName: '',
    templateType: 'CONFIRMATION',
    subjectTemplate: '',
    bodyTemplate: '',
    recipientEmails: [],
    ccEmails: [],
    isDefault: false,
    projectId: undefined
  })
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPreview, setShowPreview] = useState(false)
  const [showVariableHelper, setShowVariableHelper] = useState(false)
  const [copiedVariable, setCopiedVariable] = useState<string | null>(null)
  const [activeField, setActiveField] = useState<'subject' | 'body' | null>(null)

  // 处理邮件地址数组输入的辅助函数
  const parseEmailList = (emailString: string): string[] => {
    if (!emailString.trim()) return []
    return emailString
      .split(/[,;\s]+/)
      .map(email => email.trim())
      .filter(email => email.length > 0)
  }

  const formatEmailList = (emails: string[]): string => {
    return emails.join(', ')
  }

  useEffect(() => {
    if (template && mode === 'edit') {
      setFormData({
        id: template.id,
        templateName: template.templateName,
        templateType: template.templateType,
        subjectTemplate: template.subjectTemplate || '',
        bodyTemplate: template.bodyTemplate,
        recipientEmails: template.recipientEmails || [],
        ccEmails: template.ccEmails || [],
        isDefault: template.isDefault,
        projectId: template.projectId
      })
    } else {
      setFormData({
        templateName: '',
        templateType: 'CONFIRMATION',
        subjectTemplate: '',
        bodyTemplate: '',
        recipientEmails: [],
        ccEmails: [],
        isDefault: false,
        projectId: undefined
      })
    }
    setErrors({})
    setShowPreview(false)
    setShowVariableHelper(false)
    setCopiedVariable(null)
    setActiveField(null)
  }, [template, mode, isOpen])

  useEffect(() => {
    if (isOpen) {
      loadProjects()
    }
  }, [isOpen])

  const loadProjects = async () => {
    try {
      const response = await fetch('/api/projects?limit=100', {
        credentials: 'include'
      })
      const result = await response.json()
      if (result.success) {
        setProjects(result.data.projects)
      }
    } catch (error) {
      console.error('Failed to load projects:', error)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.templateName?.trim()) {
      newErrors.templateName = '模板名称不能为空'
    }

    if (!formData.templateType) {
      newErrors.templateType = '请选择模板类型'
    }

    if (!formData.bodyTemplate?.trim()) {
      newErrors.bodyTemplate = '邮件正文不能为空'
    }

    // 验证邮件地址格式

    if (formData.recipientEmails && formData.recipientEmails.length > 0) {
      const invalidRecipients = formData.recipientEmails.filter(email =>
        email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
      )
      if (invalidRecipients.length > 0) {
        newErrors.recipientEmails = '请输入有效的收件人邮箱地址'
      }
    }

    if (formData.ccEmails && formData.ccEmails.length > 0) {
      const invalidCcEmails = formData.ccEmails.filter(email =>
        email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
      )
      if (invalidCcEmails.length > 0) {
        newErrors.ccEmails = '请输入有效的抄送人邮箱地址'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      await onSubmit(formData)
      onClose()
    } catch (error) {
      console.error('Submit failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const renderPreview = () => {
    const mockVariables = {
      user_name: '赵六',
      user_email: '<EMAIL>',
      project_code: 'MRG004A-001',
      project_name: 'MRG004A临床试验',
      protocol_number: 'MRG004A-001',
      subject_number: '001',
      center_number: '110',
      event_name: '严重不良事件',
      learned_date: '2024-01-15',
      seriousness: '危及生命',
      processor_user: '张三',
      qc_user: '李四',
      de_user: '王五',
    }

    let previewSubject = formData.subjectTemplate || ''
    let previewBody = formData.bodyTemplate || ''

    // 简单的变量替换
    Object.entries(mockVariables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
      previewSubject = previewSubject.replace(regex, value)
      previewBody = previewBody.replace(regex, value)
    })

    return (
      <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
        <h4 className="font-medium text-gray-900 mb-2">预览效果</h4>
        {previewSubject && (
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">主题:</label>
            <div className="text-sm text-gray-900 bg-white p-2 rounded border">
              {previewSubject}
            </div>
          </div>
        )}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">正文:</label>
          <div className="text-sm text-gray-900 bg-white p-3 rounded border whitespace-pre-wrap">
            {previewBody}
          </div>
        </div>
      </div>
    )
  }

  const handleVariableClick = async (variableName: string) => {
    try {
      // 如果有活动字段，直接插入变量
      if (activeField === 'subject') {
        const currentValue = formData.subjectTemplate || ''
        setFormData(prev => ({
          ...prev,
          subjectTemplate: currentValue + variableName
        }))
      } else if (activeField === 'body') {
        const currentValue = formData.bodyTemplate || ''
        setFormData(prev => ({
          ...prev,
          bodyTemplate: currentValue + variableName
        }))
      } else {
        // 否则复制到剪贴板
        await navigator.clipboard.writeText(variableName)
      }

      setCopiedVariable(variableName)
      // 2秒后清除复制状态
      setTimeout(() => setCopiedVariable(null), 2000)
    } catch (err) {
      console.error('操作失败:', err)
    }
  }

  const renderVariableHelper = () => {
    const variableCategories = [
      {
        title: '用户变量',
        description: '当前操作用户的信息',
        variables: [
          { name: '{{user_name}}', description: '用户姓名', example: '张三' },
          { name: '{{user_email}}', description: '用户邮箱', example: '<EMAIL>' }
        ]
      },
      {
        title: '项目变量',
        description: '项目相关信息',
        variables: [
          { name: '{{project_code}}', description: '项目编号', example: 'MRG004A-001' },
          { name: '{{project_name}}', description: '项目名称', example: 'MRG004A临床试验' },
          { name: '{{study_title}}', description: '研究标题', example: 'MRG004A药物安全性研究' },
          { name: '{{pv_email}}', description: 'PV邮箱', example: '<EMAIL>' },
          { name: '{{sponsor_contact}}', description: '申办方联系人', example: '申办方公司' }
        ]
      },
      {
        title: '提取信息变量',
        description: '从邮件中提取的SAE相关信息',
        variables: [
          { name: '{{protocol_number}}', description: '方案编号', example: 'MRG004A-001' },
          { name: '{{subject_number}}', description: '受试者编号', example: '001' }, 
          { name: '{{event_name}}', description: '事件名称', example: '严重不良事件' },
          { name: '{{learned_date}}', description: '获知日期', example: '2024-01-15' },
          { name: '{{seriousness}}', description: '严重程度', example: '危及生命' },
          { name: '{{causality}}', description: '因果关系', example: '可能相关' }, 
        ]
      },
      {
        title: '任务信息变量',
        description: '任务的处理相关信息',
        variables: [
          { name: '{{processor_user}}', description: '处理人', example: '张三' },
          { name: '{{qc_user}}', description: 'QC人', example: '李四' },
          { name: '{{de_user}}', description: '录入', example: '王五' },
          { name: '{{day0}}', description: '任务创建日期', example: '2024年1月15日' },
          { name: '{{day1}}', description: '任务创建日期的第二天', example: '2024年1月16日' },
        ]
      },
      {
        title: '报告变量',
        description: '报告状态和类型信息',
        variables: [
          { name: '{{report_type}}', description: '报告类型', example: 'INITIAL' },
          { name: '{{report_type_text}}', description: '报告类型文本', example: '首次报告' },
          { name: '{{report_status}}', description: '报告状态', example: 'PENDING' },
          { name: '{{report_created_at}}', description: '报告创建时间', example: '2024-01-15 10:30:00' }
        ]
      }
    ]

    return (
      <div className="border border-blue-200 rounded-md p-4 bg-blue-50">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-blue-900">可用变量参考</h4>
          <button
            type="button"
            onClick={() => setShowVariableHelper(false)}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            收起
          </button>
        </div>

        <div className="space-y-4 max-h-96 overflow-y-auto">
          {variableCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="bg-white rounded-md p-3">
              <h5 className="font-medium text-gray-900 mb-1">{category.title}</h5>
              <p className="text-sm text-gray-600 mb-2">{category.description}</p>
              <div className="space-y-1">
                {category.variables.map((variable, varIndex) => (
                  <div key={varIndex} className="flex items-center justify-between text-sm">
                    <div className="flex-1">
                      <button
                        type="button"
                        onClick={() => handleVariableClick(variable.name)}
                        className={`px-2 py-1 rounded font-mono text-xs transition-colors cursor-pointer border-none ${
                          copiedVariable === variable.name
                            ? 'bg-green-100 text-green-700'
                            : 'bg-gray-100 hover:bg-blue-100 text-blue-600'
                        }`}
                        title={
                          copiedVariable === variable.name
                            ? '已复制!'
                            : activeField
                              ? `点击插入到${activeField === 'subject' ? '主题' : '正文'}字段`
                              : '点击复制到剪贴板'
                        }
                      >
                        {copiedVariable === variable.name ? '✓ 已复制' : variable.name}
                      </button>
                      <span className="ml-2 text-gray-700">{variable.description}</span>
                    </div>
                    <span className="text-gray-500 text-xs ml-2">
                      示例: {variable.example}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
          <strong>使用提示:</strong>
          {activeField ? (
            <span>点击任意变量名可直接插入到当前编辑的{activeField === 'subject' ? '主题' : '正文'}字段中。</span>
          ) : (
            <span>先点击主题或正文输入框，然后点击变量名可直接插入；或点击变量名复制到剪贴板。</span>
          )}
        </div>
      </div>
    )
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? '新建邮件模板' : '编辑邮件模板'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="模板名称 *"
            value={formData.templateName || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, templateName: e.target.value }))}
            error={errors.templateName}
            placeholder="例如：默认确认回复模板"
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              模板类型 *
            </label>
            <select
              value={formData.templateType || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, templateType: e.target.value }))}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            >
              {templateTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.templateType && (
              <p className="text-sm text-red-600 mt-1">{errors.templateType}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            关联项目 (可选)
          </label>
          <select
            value={formData.projectId || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, projectId: e.target.value ? parseInt(e.target.value) : undefined }))}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
          >
            <option value="">通用模板</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>
                {project.projectCode} - {project.projectName}
              </option>
            ))}
          </select>
        </div>

        {/* 邮件收发人配置 */}
        <div className="space-y-4 border-t pt-4">
          <h3 className="text-lg font-medium text-gray-900">邮件收发人配置</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              收件人邮箱地址
            </label>
            <textarea
              value={formatEmailList(formData.recipientEmails || [])}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                recipientEmails: parseEmailList(e.target.value)
              }))}
              rows={3}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
              placeholder="例如：<EMAIL>, <EMAIL>"
            />
            {errors.recipientEmails && (
              <p className="text-sm text-red-600 mt-1">{errors.recipientEmails}</p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              多个邮箱地址请用英文逗号分隔。留空则在发送时动态确定收件人。
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              抄送人邮箱地址
            </label>
            <textarea
              value={formatEmailList(formData.ccEmails || [])}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                ccEmails: parseEmailList(e.target.value)
              }))}
              rows={2}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
              placeholder="例如：<EMAIL>, <EMAIL>"
            />
            {errors.ccEmails && (
              <p className="text-sm text-red-600 mt-1">{errors.ccEmails}</p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              多个邮箱地址请用英文逗号分隔。可选字段。
            </p>
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              邮件主题模板
            </label>
            <button
              type="button"
              onClick={() => setShowVariableHelper(!showVariableHelper)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {showVariableHelper ? '隐藏变量帮助' : '查看可用变量'}
            </button>
          </div>
          <input
            type="text"
            value={formData.subjectTemplate || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, subjectTemplate: e.target.value }))}
            onFocus={() => setActiveField('subject')}
            onBlur={() => setActiveField(null)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            placeholder="例如：确认收到SAE报告 - {{protocol_number}}"
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              邮件正文模板 *
            </label>
            {!showVariableHelper && (
              <button
                type="button"
                onClick={() => setShowVariableHelper(true)}
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                查看可用变量
              </button>
            )}
          </div>

          {showVariableHelper && renderVariableHelper()}

          <textarea
            value={formData.bodyTemplate || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, bodyTemplate: e.target.value }))}
            onFocus={() => setActiveField('body')}
            onBlur={() => setActiveField(null)}
            rows={8}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            placeholder={`尊敬的{{user_name}}，您好！

我们已收到您提交的SAE报告...

此致
敬礼！`}
          />
          {errors.bodyTemplate && (
            <p className="text-sm text-red-600 mt-1">{errors.bodyTemplate}</p>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isDefault"
              checked={formData.isDefault || false}
              onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-900">
              设为默认模板
            </label>
          </div>
          
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? '隐藏预览' : '预览效果'}
          </Button>
        </div>

        {showPreview && renderPreview()}

        <ModalFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
          >
            {mode === 'create' ? '创建模板' : '保存更改'}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  )
}
