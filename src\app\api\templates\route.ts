import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getCurrentUser, hasPermission } from '@/lib/auth'
import { EmailTemplateType } from 'generated-prisma'

const createTemplateSchema = z.object({
  templateName: z.string().min(1, '请输入模板名称'),
  templateType: z.enum(['CONFIRMATION', 'SAFETY_NOTIFICATION', 'INTERNAL_FORWARD', 'WM_FORWARD']),
  subjectTemplate: z.string().optional(),
  bodyTemplate: z.string().min(1, '请输入邮件正文模板'),
  recipientEmails: z.array(z.string().email('请输入有效的收件人邮箱')).optional(),
  ccEmails: z.array(z.string().email('请输入有效的抄送人邮箱')).optional(),
  attachmentConfig: z.any().optional(),
  variableMapping: z.any().optional(),
  projectId: z.number().optional(),
  isDefault: z.boolean().default(false)
})

const updateTemplateSchema = createTemplateSchema.partial().extend({
  id: z.number()
})

/**
 * 获取邮件模板列表
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const search = url.searchParams.get('search') || ''
    const templateType = url.searchParams.get('templateType') as EmailTemplateType | null
    const projectId = url.searchParams.get('projectId')

    const skip = (page - 1) * limit

    const where: any = {
      AND: [
        search ? {
          OR: [
            { templateName: { contains: search } },
            { bodyTemplate: { contains: search } }
          ]
        } : {},
        templateType ? { templateType } : {},
        projectId ? { projectId: parseInt(projectId) } : {}
      ]
    }

    // 如果不是PM，只能看到自己项目的模板或通用模板
    if (!hasPermission(user.roles, ['PM'])) {
      const userProjectIds = await prisma.userProject.findMany({
        where: { userId: user.id },
        select: { projectId: true }
      }).then(ups => ups.map(up => up.projectId))

      where.AND.push({
        OR: [
          { projectId: null }, // 通用模板
          { projectId: { in: userProjectIds } } // 用户项目的模板
        ]
      })
    }

    const [templates, total] = await Promise.all([
      prisma.emailTemplate.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          }
        }
      }),
      prisma.emailTemplate.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        templates,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get templates failed:', error)
    return NextResponse.json({
      success: false,
      error: '获取模板列表失败'
    }, { status: 500 })
  }
}

/**
 * 创建邮件模板
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM', 'Triage'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const templateData = createTemplateSchema.parse(body)

    // 如果指定了项目，检查用户是否有权限
    if (templateData.projectId) {
      if (!hasPermission(user.roles, ['PM'])) {
        const hasAccess = await prisma.userProject.findFirst({
          where: {
            userId: user.id,
            projectId: templateData.projectId
          }
        })

        if (!hasAccess) {
          return NextResponse.json({
            success: false,
            error: '没有该项目的权限'
          }, { status: 403 })
        }
      }

      // 检查项目是否存在
      const project = await prisma.project.findUnique({
        where: { id: templateData.projectId }
      })

      if (!project) {
        return NextResponse.json({
          success: false,
          error: '项目不存在'
        }, { status: 400 })
      }
    }

    // 创建模板
    const newTemplate = await prisma.emailTemplate.create({
      data: {
        ...templateData,
        templateType: templateData.templateType as EmailTemplateType
      },
      include: {
        project: {
          select: {
            id: true,
            projectCode: true,
            projectName: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: { template: newTemplate },
      message: '模板创建成功'
    })

  } catch (error) {
    console.error('Create template failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: '创建模板失败'
    }, { status: 500 })
  }
}

/**
 * 更新邮件模板
 */
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM', 'Triage'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    if(body.projectId === null){
      delete body.projectId;
    }
    
    const { id, ...updateData } = updateTemplateSchema.parse(body) 
    // 检查模板是否存在
    const existingTemplate = await prisma.emailTemplate.findUnique({
      where: { id },
      include: { project: true }
    })

    if (!existingTemplate) {
      return NextResponse.json({
        success: false,
        error: '模板不存在'
      }, { status: 404 })
    }

    // 检查权限
    if (!hasPermission(user.roles, ['PM'])) {
      if (existingTemplate.projectId) {
        const hasAccess = await prisma.userProject.findFirst({
          where: {
            userId: user.id,
            projectId: existingTemplate.projectId
          }
        })

        if (!hasAccess) {
          return NextResponse.json({
            success: false,
            error: '没有该模板的权限'
          }, { status: 403 })
        }
      }
    }



    // 如果更新了项目，检查权限和存在性
    if (updateData.projectId) {
      if (updateData.projectId) {
        if (!hasPermission(user.roles, ['PM'])) {
          const hasAccess = await prisma.userProject.findFirst({
            where: {
              userId: user.id,
              projectId: updateData.projectId
            }
          })

          if (!hasAccess) {
            return NextResponse.json({
              success: false,
              error: '没有该项目的权限'
            }, { status: 403 })
          }
        }

        const project = await prisma.project.findUnique({
          where: { id: updateData.projectId }
        })

        if (!project) {
          return NextResponse.json({
            success: false,
            error: '项目不存在'
          }, { status: 400 })
        }
      }
    }

    // 更新模板
    const updatedTemplate = await prisma.emailTemplate.update({
      where: { id },
      data: {
        ...updateData,
        ...(updateData.templateType && { templateType: updateData.templateType as EmailTemplateType })
      },
      include: {
        project: {
          select: {
            id: true,
            projectCode: true,
            projectName: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: { template: updatedTemplate },
      message: '模板更新成功'
    })

  } catch (error) {
    console.error('Update template failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: '更新模板失败'
    }, { status: 500 })
  }
}
