import { fromBuff<PERSON> } from 'pdf2pic'
import sharp from 'sharp'
import path from 'path'
import fs from 'fs/promises'
import { AIExtractor } from './aiExtractor'

export interface PDFProcessingOptions {
  density?: number
  imageWidth?: number
  imageHeight?: number
  imageQuality?: number
  maxConcurrency?: number // 新增：最大并发数
  testMode?: boolean // 新增：测试模式
}

export interface PDFProcessingResult {
  success: boolean
  extractedText?: string
  pageCount?: number
  hasSignature: boolean
  isPdfComplete: boolean
  isPageNumberContinuity: boolean
  error?: string
  processingTime?: number
  pageResults?: PageProcessingResult[]
}

export interface PageProcessingResult {
  pageNumber: number
  realPageNumber: number | null
  success: boolean
  hasSignature: boolean
  text?: string
  textLength?: number
  error?: string
  processingTime?: number
}

export class PDFProcessorService {
  private static readonly DEFAULT_OPTIONS: PDFProcessingOptions = {
    density: 200,        // 降低分辨率以提高速度
    imageWidth: 1000,    // 减小图片尺寸
    imageHeight: 1400,   // 减小图片尺寸
    imageQuality: 85,    // 降低质量以减少处理时间
    maxConcurrency: 20   // 默认并发数
  }
 
  /**
   * 高质量处理PDF文件（用于最终处理）
   */
  static async processPdf(
    pdfBuffer: Buffer,
    fileName: string,
    options: PDFProcessingOptions = {}
  ): Promise<PDFProcessingResult> {
    return await this.processPdfWithOCR(pdfBuffer, fileName, options)
  }

  /**
   * 处理PDF文件，将其转换为图片后进行OCR识别
   */
  static async processPdfWithOCR(
    pdfBuffer: Buffer,
    fileName: string,
    options: PDFProcessingOptions = {}
  ): Promise<PDFProcessingResult> {
    const startTime = Date.now()
    const config = {
      ...this.DEFAULT_OPTIONS,
      ...options
    }

    try {
      console.log(`开始PDF转图片处理: ${fileName}`)

      // 验证PDF缓冲区
      if (!pdfBuffer || pdfBuffer.length === 0) {
        throw new Error('PDF缓冲区为空')
      }

      // 直接从缓冲区转换PDF为图片
      const pages = await this.convertPdfBufferToImages(pdfBuffer, config)
      console.log(`PDF转换完成，共${pages.length}页`)

      // 处理每页图片进行OCR
      const pageResults = await this.processPages(pages, config)
      console.log(pageResults)

      // 合并结果
      const result = this.combinePageResults(pageResults, startTime, fileName)

      return result

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error(`PDF处理失败: ${fileName}`, error)

      return {
        isPageNumberContinuity:false,
        isPdfComplete: false,
        hasSignature: false,
        success: false,
        error: error instanceof Error ? error.message : 'PDF处理失败',
        processingTime
      }
    }
  }
 

  /**
   * 将PDF缓冲区转换为图片
   */
  private static async convertPdfBufferToImages(
    pdfBuffer: Buffer,
    config: PDFProcessingOptions
  ): Promise<any[]> {
    const convert = fromBuffer(pdfBuffer, {
      density: config.density,
      format: 'png',      
      preserveAspectRatio: true,
      width: config.imageWidth,
      height: config.imageHeight,
      quality: config.imageQuality
    })

    // 转换所有页面
    const pages = await convert.bulk(-1, { responseType: 'buffer' })

    if (!pages || pages.length === 0) {
      throw new Error('PDF转换失败，未生成任何图片')
    }

    return pages
  }

  /**
   * 处理所有页面进行OCR（支持并行处理）
   */
  private static async processPages(
    pages: any[],
    config: PDFProcessingOptions
  ): Promise<PageProcessingResult[]> {
    const aiExtractor = new AIExtractor()
    const maxConcurrency = config.maxConcurrency || 3
    
    // 使用并发控制
    console.log(`使用并发处理模式，共${pages.length}页，最大并发数: ${maxConcurrency}`)
    
    const results: PageProcessingResult[] = []
    const executing: Promise<void>[] = []
    
    for (let i = 0; i < pages.length; i++) {
      const promise = this.processPage(pages[i], i + 1, aiExtractor, config)
        .then(result => {
          results[i] = result
        })
      
      executing.push(promise)
      
      // 控制并发数
      if (executing.length >= maxConcurrency) {
        await Promise.race(executing)
        executing.splice(executing.findIndex(p => p === promise), 1)
      }
    }
    
    // 等待所有任务完成
    await Promise.all(executing)
    
    return results
  }

  /**
   * 处理单页图片进行OCR
   */
  private static async processPage(
    page: any, 
    pageNumber: number, 
    aiExtractor: AIExtractor, 
    config: PDFProcessingOptions
  ): Promise<PageProcessingResult> {
    const startTime = Date.now()
    
    try {
      const pageBuffer = page.buffer
      if (!pageBuffer) {
        throw new Error('页面缓冲区为空')
      }
       
      const base64Image = pageBuffer.toString('base64')
      const rev = await aiExtractor.extractTextFromImage(
        base64Image, 
      )
      const text = rev.text;
      
      const processingTime = Date.now() - startTime
      
      console.log(`第${pageNumber}页OCR完成，文本长度: ${text.length}，耗时: ${processingTime}ms`)
      
      return {
        pageNumber,
        realPageNumber: rev.pageNumber,
        success: true,
        text,
        hasSignature:rev.hasSignature,
        textLength: text.length,
        processingTime
      }
      
    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error(`第${pageNumber}页处理失败:`, error)
      
      return {
        hasSignature:false,
        pageNumber,
        realPageNumber: null,
        success: false,
        error: error instanceof Error ? error.message : '页面处理失败',
        processingTime
      }
    }
  } 
 
  /**
   * 合并页面处理结果
   */
  private static combinePageResults(
    pageResults: PageProcessingResult[], 
    startTime: number, 
    fileName: string
  ): PDFProcessingResult {
    const processingTime = Date.now() - startTime 
    
    // 合并所有页面文本 
    const extractedText = pageResults.map(v => v.text ).join('\n\n')
    
    // 检测签名和完整性
    const hasSignature = !!pageResults.find( p => p.hasSignature)
    const isPdfComplete = this.checkPdfCompleteness(pageResults.length, extractedText) 
    //  检查页码是否连续
    const isPageNumberContinuity = this.checkPageNumberContinuity(pageResults)
    
    console.log(`PDF处理完成: ${fileName}, 成功页面: ${pageResults.length}/${pageResults.length}, 耗时: ${processingTime}ms`)
    
    return {
      success: true,
      extractedText,
      pageCount: pageResults.length,
      hasSignature,
      isPageNumberContinuity,
      isPdfComplete, 
      processingTime,
      pageResults
    }
  }
 

  /**
   * 检查PDF完整性
   */
  private static checkPdfCompleteness(pageCount: number, extractedText: string): boolean {
    // 基本检查：页面数量和文本长度
    if (pageCount === 0 || extractedText.trim().length < 100) {
      return false
    }

    // 检查是否包含常见的SAE报告关键字段
    const requiredFields = [
      '方案编号', '受试者编号', '事件名称', '严重性', '获知日期',
      'protocol', 'subject', 'event', 'serious', 'learned'
    ]

    const lowerText = extractedText.toLowerCase()
    const foundFields = requiredFields.filter(field =>
      lowerText.includes(field.toLowerCase())
    )

    // 至少包含40%的关键字段
    return foundFields.length >= requiredFields.length * 0.4
  }

  private static checkPageNumberContinuity(pageResults: PageProcessingResult[]){
    const pages = pageResults.map(p => p.realPageNumber).filter(v => v !== null); 
    if (pages.length <= 1) return true;

    const sorted = [...new Set(pages)].sort((a, b) => a - b);
    const min = sorted[0];
    const max = sorted[sorted.length - 1];

    return max - min + 1 === sorted.length;
  }
  
}
