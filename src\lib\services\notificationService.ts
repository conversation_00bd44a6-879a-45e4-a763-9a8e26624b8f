import { prisma } from '@/lib/db'
import { EWSMailService } from './ewsMailService'
import { EmailTemplateType, UserRole } from 'generated-prisma'
import { retry } from '@/lib/decorators/retry'
import type { EMLFileResult } from './emailTemplateService'

export interface NotificationRecipient {
  id: number
  name: string
  email: string
  roles: UserRole[]
}

export interface RoleBasedNotification {
  recipient: NotificationRecipient
  templateType: EmailTemplateType
  emlFile?: EMLFileResult
  subject: string
  body: string
  attachments: string[]
}

export interface NotificationResult {
  success: boolean
  sentCount: number
  failedCount: number
  results: Array<{
    recipientEmail: string
    templateType: EmailTemplateType
    success: boolean
    error?: string
  }>
}

/**
 * 通知服务
 * 负责根据用户角色发送不同类型的通知邮件
 */
export class NotificationService {
  
  /**
   * 发送基于角色的通知
   */
  @retry()
  async sendRoleBasedNotifications(
    reportId: number,
    projectId: number,
    assignedPersonnel: {
      processor?: NotificationRecipient
      dataEntry?: NotificationRecipient
      qualityControl?: NotificationRecipient
    },
    emlFiles: Map<EmailTemplateType, EMLFileResult>,
    senderCredentials: { domainAccount: string; password: string }
  ): Promise<NotificationResult> {
    try {
      console.log('开始发送基于角色的通知邮件...')
      
      const notifications = await this.buildRoleBasedNotifications(
        reportId,
        projectId,
        assignedPersonnel,
        emlFiles
      )
      
      const results: NotificationResult['results'] = []
      let sentCount = 0
      let failedCount = 0
      
      // 发送通知邮件
      for (const notification of notifications) {
        try {
          await this.sendNotificationEmail(notification, senderCredentials)
          
          results.push({
            recipientEmail: notification.recipient.email,
            templateType: notification.templateType,
            success: true
          })
          
          sentCount++
          console.log(`通知邮件发送成功: ${notification.recipient.email} (${notification.templateType})`)
          
        } catch (error) {
          results.push({
            recipientEmail: notification.recipient.email,
            templateType: notification.templateType,
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
          })
          
          failedCount++
          console.error(`通知邮件发送失败: ${notification.recipient.email}`, error)
        }
      }
      
      console.log(`通知邮件发送完成: 成功 ${sentCount}, 失败 ${failedCount}`)
      
      return {
        success: true,
        sentCount,
        failedCount,
        results
      }
      
    } catch (error) {
      console.error('发送基于角色的通知失败:', error)
      return {
        success: false,
        sentCount: 0,
        failedCount: 0,
        results: []
      }
    }
  }
  
  /**
   * 构建基于角色的通知
   */
  private async buildRoleBasedNotifications(
    reportId: number,
    projectId: number,
    assignedPersonnel: {
      processor?: NotificationRecipient
      dataEntry?: NotificationRecipient
      qualityControl?: NotificationRecipient
    },
    emlFiles: Map<EmailTemplateType, EMLFileResult>
  ): Promise<RoleBasedNotification[]> {
    const notifications: RoleBasedNotification[] = []
    
    // 获取SAE报告信息
    const report = await prisma.sAEReport.findUnique({
      where: { id: reportId },
      include: {
        project: true
      }
    })
    
    if (!report) {
      throw new Error(`SAE报告不存在: ${reportId}`)
    }
    
    // 为处理人员(Triage)发送确认邮件
    if (assignedPersonnel.processor) {
      const confirmationEml = emlFiles.get(EmailTemplateType.CONFIRMATION)
      notifications.push({
        recipient: assignedPersonnel.processor,
        templateType: EmailTemplateType.CONFIRMATION,
        emlFile: confirmationEml,
        subject: `SAE报告确认通知 - ${report.protocolNumber || 'Unknown'}`,
        body: this.buildNotificationBody('CONFIRMATION', report, assignedPersonnel.processor),
        attachments: confirmationEml ? [confirmationEml.filePath] : []
      })
    }
    
    // 为数据录入人员(PM)发送内部转发邮件
    if (assignedPersonnel.dataEntry) {
      const internalEml = emlFiles.get(EmailTemplateType.INTERNAL_FORWARD)
      notifications.push({
        recipient: assignedPersonnel.dataEntry,
        templateType: EmailTemplateType.INTERNAL_FORWARD,
        emlFile: internalEml,
        subject: `SAE报告数据录入通知 - ${report.protocolNumber || 'Unknown'}`,
        body: this.buildNotificationBody('INTERNAL_FORWARD', report, assignedPersonnel.dataEntry),
        attachments: internalEml ? [internalEml.filePath] : []
      })
    }
    
    // 为质控人员(WM)发送WM转发邮件和安全通知
    if (assignedPersonnel.qualityControl) {
      const wmEml = emlFiles.get(EmailTemplateType.WM_FORWARD)
      const safetyEml = emlFiles.get(EmailTemplateType.SAFETY_NOTIFICATION)
      
      if (wmEml) {
        notifications.push({
          recipient: assignedPersonnel.qualityControl,
          templateType: EmailTemplateType.WM_FORWARD,
          emlFile: wmEml,
          subject: `SAE报告质控通知 - ${report.protocolNumber || 'Unknown'}`,
          body: this.buildNotificationBody('WM_FORWARD', report, assignedPersonnel.qualityControl),
          attachments: [wmEml.filePath]
        })
      }
      
      if (safetyEml) {
        notifications.push({
          recipient: assignedPersonnel.qualityControl,
          templateType: EmailTemplateType.SAFETY_NOTIFICATION,
          emlFile: safetyEml,
          subject: `SAE安全通知 - ${report.protocolNumber || 'Unknown'}`,
          body: this.buildNotificationBody('SAFETY_NOTIFICATION', report, assignedPersonnel.qualityControl),
          attachments: [safetyEml.filePath]
        })
      }
    }
    
    return notifications
  }
  
  /**
   * 构建通知邮件正文
   */
  private buildNotificationBody(
    notificationType: string,
    report: any,
    recipient: NotificationRecipient
  ): string {
    const baseInfo = `
亲爱的 ${recipient.name}，

您有一个新的SAE报告需要处理：

报告信息：
- 报告UUID: ${report.reportUuid}
- 方案编号: ${report.protocolNumber || 'N/A'}
- 受试者编号: ${report.subjectNumber || 'N/A'}
- 事件名称: ${report.eventName || 'N/A'}
- 项目名称: ${report.project?.projectName || 'N/A'}
- 报告状态: ${report.status}
- 创建时间: ${new Date(report.createdAt).toLocaleString('zh-CN')}
`
    
    switch (notificationType) {
      case 'CONFIRMATION':
        return baseInfo + `
您的任务：
- 确认SAE报告信息的准确性
- 验证附件完整性
- 如有问题请及时反馈

请登录系统查看详细信息并进行处理。

此邮件由SAE Triage系统自动发送，请勿直接回复。`
        
      case 'INTERNAL_FORWARD':
        return baseInfo + `
您的任务：
- 进行数据录入和整理
- 确保数据质量和完整性
- 按时完成录入工作

请登录系统查看详细信息并进行处理。

此邮件由SAE Triage系统自动发送，请勿直接回复。`
        
      case 'WM_FORWARD':
        return baseInfo + `
您的任务：
- 进行质量控制检查
- 审核数据准确性
- 确认报告符合规范要求

请登录系统查看详细信息并进行处理。

此邮件由SAE Triage系统自动发送，请勿直接回复。`
        
      case 'SAFETY_NOTIFICATION':
        return baseInfo + `
这是一个重要的安全通知，请优先处理。

您的任务：
- 立即评估安全风险
- 确定是否需要紧急措施
- 及时上报相关部门

请登录系统查看详细信息并进行紧急处理。

此邮件由SAE Triage系统自动发送，请勿直接回复。`
        
      default:
        return baseInfo + `
请登录系统查看详细信息并进行相应处理。

此邮件由SAE Triage系统自动发送，请勿直接回复。`
    }
  }
  
  /**
   * 发送单个通知邮件
   */
  private async sendNotificationEmail(
    notification: RoleBasedNotification,
    senderCredentials: { domainAccount: string; password: string }
  ): Promise<void> {
    const ewsService = new EWSMailService()
    
    try {
      // 连接EWS服务
      await ewsService.connect(senderCredentials.domainAccount, senderCredentials.password)
      
      // 发送邮件
      await this.sendEmailWithEWS(ewsService, {
        to: [notification.recipient.email],
        subject: notification.subject,
        body: notification.body,
        attachments: notification.attachments
      })
      
    } finally {
      ewsService.disconnect()
    }
  }
  
  /**
   * 使用EWS发送邮件
   */
  private async sendEmailWithEWS(
    ewsService: EWSMailService,
    emailData: {
      to: string[]
      subject: string
      body: string
      attachments: string[]
    }
  ): Promise<void> {
    // 这里需要实现EWS邮件发送逻辑
    // 由于EWSMailService目前只有读取功能，这里先记录日志
    console.log('模拟发送邮件:', {
      to: emailData.to,
      subject: emailData.subject,
      attachmentCount: emailData.attachments.length
    })
    
    // TODO: 实现实际的EWS邮件发送功能
    // 或者使用其他邮件发送服务
  }
}

// 导出单例实例
export const notificationService = new NotificationService()
