import { prisma } from '@/lib/db'

class ProjectService {
  /**
   * 根据项目代码匹配项目
   * @param projectCode 项目代码
   * @returns 匹配的项目信息，如果未找到则返回null
   */
  async matchProject(projectCode: string) {
    try {
      if (!projectCode || typeof projectCode !== 'string') {
        return null
      }

      // 从数据库中查询匹配的项目
      const project = await prisma.project.findFirst({
        where: {
          projectCode: projectCode.trim(),
          isActive: true // 只查询活跃的项目
        },
        include: {
          userProjects: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  roles: true
                }
              }
            }
          }
        }
      })

      if (!project) {
        return null
      }

      // 格式化返回数据
      return {
        ...project,
        users: project.userProjects.map(up => ({
          ...up.user,
          roleInProject: up.roleInProject,
          isPrimary: up.isPrimary
        }))
      }

    } catch (error) {
      console.error('匹配项目失败:', error)
      return null
    }
  }

  /**
   * 根据项目代码模糊匹配项目列表
   * @param projectCode 项目代码（支持模糊匹配）
   * @param limit 返回结果数量限制，默认10
   * @returns 匹配的项目列表
   */
  async searchProjects(projectCode: string, limit: number = 10) {
    try {
      if (!projectCode || typeof projectCode !== 'string') {
        return []
      }

      const projects = await prisma.project.findMany({
        where: {
          OR: [
            {
              projectCode: {
                contains: projectCode.trim()
              }
            },
            {
              projectName: {
                contains: projectCode.trim()
              }
            }
          ],
          isActive: true
        },
        select: {
          id: true,
          projectCode: true,
          projectName: true,
          sponsor: true,
          studyTitle: true,
          description: true,
          isActive: true
        },
        take: limit,
        orderBy: [
          // 优先精确匹配项目代码
          {
            projectCode: 'asc'
          },
          {
            createdAt: 'desc'
          }
        ]
      })

      return projects

    } catch (error) {
      console.error('搜索项目失败:', error)
      return []
    }
  }
}

export const projectService = new ProjectService()