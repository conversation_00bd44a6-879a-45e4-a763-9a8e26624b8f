'use client'

import { useEffect, useState } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/Button'
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell, TableActionHead, TableActionCell } from '@/components/ui/Table'
import { TemplateForm } from '@/components/forms/TemplateForm'
import { formatDate } from '@/lib/utils'

interface EmailTemplate {
  id: number
  templateName: string
  templateType: string
  subjectTemplate?: string
  bodyTemplate: string
  recipientEmails?: string[]
  ccEmails?: string[]
  isDefault: boolean
  createdAt: string
  project?: {
    id: number
    projectCode: string
    projectName: string
  }
}

interface TemplatesResponse {
  success: boolean
  data: {
    templates: EmailTemplate[]
    error?: string;
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

const templateTypeMap = {
  'CONFIRMATION': '确认回复',
  'SAFETY_NOTIFICATION': '安全性通知',
  'INTERNAL_FORWARD': '内部转发',
  'WM_FORWARD': 'WM转发'
}

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  // 表单状态
  const [showForm, setShowForm] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')

  useEffect(() => {
    loadTemplates()
  }, [page, search, typeFilter])

  const loadTemplates = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search }),
        ...(typeFilter && { templateType: typeFilter })
      })

      const response = await fetch(`/api/templates?${params}`, {
        credentials: 'include'
      })

      const result: TemplatesResponse = await response.json()

      if (result.success) {
        setTemplates(result.data.templates)
        setPagination(result.data.pagination)
      } else {
        setError(result.data?.error || '获取模板列表失败')
      }
    } catch (error) {
      console.error('Load templates failed:', error)
      setError('获取模板列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPage(1)
    loadTemplates()
  }

  const handleCreateTemplate = () => {
    setEditingTemplate(null)
    setFormMode('create')
    setShowForm(true)
  }

  const handleEditTemplate = (template: EmailTemplate) => {
    setEditingTemplate(template)
    setFormMode('edit')
    setShowForm(true)
  }

  const handleDeleteTemplate = async (template: EmailTemplate) => {
    if (!confirm(`确定要删除模板 "${template.templateName}" 吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/templates`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ id: template.id })
      })

      const result = await response.json()

      if (result.success) {
        loadTemplates() // 重新加载列表
      } else {
        setError(result.error || '删除模板失败')
      }
    } catch (error) {
      console.error('Delete template failed:', error)
      setError('删除模板失败')
    }
  }

  const handleFormSubmit = async (templateData: Partial<EmailTemplate>) => {
    try {
      const url = '/api/templates'
      const method = formMode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(templateData)
      })

      const result = await response.json()

      if (result.success) {
        loadTemplates() // 重新加载列表
        setShowForm(false)
      } else {
        throw new Error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('Form submit failed:', error)
      throw error
    }
  }

  if (isLoading && templates.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">邮件模板</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理各种邮件通知模板
          </p>
        </div>
        <Button onClick={handleCreateTemplate}>
          新建模板
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="搜索模板名称或内容..."
              className="form-input w-full"
            />
          </div>
          <div className="w-48">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="form-input w-full"
            >
              <option value="">所有类型</option>
              <option value="CONFIRMATION">确认回复</option>
              <option value="SAFETY_NOTIFICATION">安全性通知</option>
              <option value="INTERNAL_FORWARD">内部转发</option>
              <option value="WM_FORWARD">WM转发</option>
            </select>
          </div>
          <Button type="submit">
            搜索
          </Button>
        </form>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* 模板列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {templates.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>模板信息</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>邮件配置</TableHead>
                <TableHead>项目</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableActionHead>操作</TableActionHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {templates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {template.templateName}
                      </div>
                      {template.subjectTemplate && (
                        <div className="text-sm text-gray-500">
                          主题: {template.subjectTemplate.substring(0, 50)}
                          {template.subjectTemplate.length > 50 ? '...' : ''}
                        </div>
                      )}
                      <div className="text-xs text-gray-400">
                        {template.bodyTemplate.substring(0, 80)}
                        {template.bodyTemplate.length > 80 ? '...' : ''}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {templateTypeMap[template.templateType as keyof typeof templateTypeMap] || template.templateType}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {template.recipientEmails && template.recipientEmails.length > 0 && (
                        <div className="text-xs">
                          <span className="text-gray-500">收件人:</span> {template.recipientEmails.length}个
                        </div>
                      )}
                      {template.ccEmails && template.ccEmails.length > 0 && (
                        <div className="text-xs">
                          <span className="text-gray-500">抄送:</span> {template.ccEmails.length}个
                        </div>
                      )}
                      {(!template.recipientEmails || template.recipientEmails.length === 0) && (!template.ccEmails || template.ccEmails.length === 0) && (
                        <span className="text-xs text-gray-400">动态配置</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {template.project ? (
                      <div>
                        <div className="font-medium">{template.project.projectCode}</div>
                        <div className="text-xs text-gray-500">{template.project.projectName}</div>
                      </div>
                    ) : (
                      <span className="text-gray-500">通用模板</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      template.isDefault
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {template.isDefault ? '默认' : '自定义'}
                    </span>
                  </TableCell>
                  <TableCell>
                    {formatDate(template.createdAt)}
                  </TableCell>
                  <TableActionCell>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditTemplate(template)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        variant="danger"
                        onClick={() => handleDeleteTemplate(template)}
                      >
                        删除
                      </Button>
                    </div>
                  </TableActionCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-8">
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无模板</h3>
            <p className="mt-1 text-sm text-gray-500">
              还没有创建任何邮件模板
            </p>
            <div className="mt-6">
              <Button onClick={handleCreateTemplate}>
                创建第一个模板
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              上一页
            </button>
            <button
              onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
              disabled={page === pagination.totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              下一页
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                显示第 <span className="font-medium">{(page - 1) * pagination.limit + 1}</span> 到{' '}
                <span className="font-medium">
                  {Math.min(page * pagination.limit, pagination.total)}
                </span>{' '}
                条，共 <span className="font-medium">{pagination.total}</span> 条记录
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
                  disabled={page === pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* 模板表单 */}
      <TemplateForm
        isOpen={showForm}
        onClose={() => setShowForm(false)}
        onSubmit={handleFormSubmit}
        template={editingTemplate}
        mode={formMode}
      />
    </div>
  )
}
