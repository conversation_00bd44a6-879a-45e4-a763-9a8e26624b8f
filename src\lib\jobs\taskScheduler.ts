import { taskProcessorService } from '@/lib/services/taskProcessorService'
import logger, { taskLog } from '@/lib/logger'
import { v4 as uuidv4 } from 'uuid'

// 执行日志接口
export interface ExecutionLog {
  id: string
  startTime: Date
  endTime?: Date
  duration?: number
  status: 'running' | 'completed' | 'failed'
  processedCount: number
  failedCount: number
  error?: string
  triggeredBy?: string
}

// 调度器状态接口
export interface SchedulerStatus {
  isRunning: boolean
  lastExecutionTime?: Date
  nextScheduledTime?: Date
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
}

// 统计信息接口
export interface TaskStatistics {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageProcessingTime: number
  totalTasksProcessed: number
  totalTasksFailed: number
  lastExecutionTime?: Date
  uptime: number
}

class TaskScheduler {
  private isProcessing = false
  private executionLogs: ExecutionLog[] = []
  private statistics: TaskStatistics
  private startTime: Date
  private schedulerInterval?: NodeJS.Timeout

  constructor() {
    this.startTime = new Date()
    this.statistics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageProcessingTime: 0,
      totalTasksProcessed: 0,
      totalTasksFailed: 0,
      uptime: 0
    }

    // 启动定时任务（可选，如果需要自动执行）
    this.startScheduler()
  }

  /**
   * 检查是否正在处理任务
   */
  isCurrentlyProcessing(): boolean {
    return this.isProcessing
  }

  /**
   * 执行任务处理
   */
  async executeTaskProcessing(triggeredBy?: string): Promise<ExecutionLog> {
    if (this.isProcessing) {
      throw new Error('任务处理已在进行中')
    }

    const executionLog: ExecutionLog = {
      id: uuidv4(),
      startTime: new Date(),
      status: 'running',
      processedCount: 0,
      failedCount: 0,
      triggeredBy
    }

    this.isProcessing = true
    this.executionLogs.unshift(executionLog)
    
    // 保持最多100条执行日志
    if (this.executionLogs.length > 100) {
      this.executionLogs = this.executionLogs.slice(0, 100)
    }

    logger.info('任务调度器开始执行', {
      executionId: executionLog.id,
      triggeredBy,
      action: 'scheduler_start'
    })

    try {
      // 调用任务处理服务
      const result = await taskProcessorService.processAllPendingTasks()
      
      executionLog.endTime = new Date()
      executionLog.duration = executionLog.endTime.getTime() - executionLog.startTime.getTime()
      executionLog.processedCount = result.processedCount
      executionLog.failedCount = result.failedCount
      executionLog.status = result.success ? 'completed' : 'failed'
      
      if (!result.success) {
        executionLog.error = '批量任务处理失败'
      }

      // 更新统计信息
      this.updateStatistics(executionLog)

      logger.info('任务调度器执行完成', {
        executionId: executionLog.id,
        status: executionLog.status,
        processedCount: executionLog.processedCount,
        failedCount: executionLog.failedCount,
        duration: executionLog.duration,
        action: 'scheduler_complete'
      })

      return executionLog

    } catch (error) {
      executionLog.endTime = new Date()
      executionLog.duration = executionLog.endTime.getTime() - executionLog.startTime.getTime()
      executionLog.status = 'failed'
      executionLog.error = error instanceof Error ? error.message : '未知错误'

      this.updateStatistics(executionLog)

      logger.error('任务调度器执行失败', error as Error, {
        executionId: executionLog.id,
        duration: executionLog.duration,
        action: 'scheduler_error'
      })

      return executionLog

    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 获取调度器状态
   */
  getSchedulerStatus(): SchedulerStatus {
    const lastExecution = this.executionLogs[0]
    
    return {
      isRunning: this.isProcessing,
      lastExecutionTime: lastExecution?.startTime,
      nextScheduledTime: undefined, // 如果有定时任务，这里可以返回下次执行时间
      totalExecutions: this.statistics.totalExecutions,
      successfulExecutions: this.statistics.successfulExecutions,
      failedExecutions: this.statistics.failedExecutions
    }
  }

  /**
   * 获取最后执行状态
   */
  getLastExecutionStatus(): ExecutionLog | null {
    return this.executionLogs[0] || null
  }

  /**
   * 获取执行日志
   */
  getExecutionLogs(count: number = 10): ExecutionLog[] {
    return this.executionLogs.slice(0, count)
  }

  /**
   * 获取统计信息
   */
  getStatistics(): TaskStatistics {
    return {
      ...this.statistics,
      uptime: Date.now() - this.startTime.getTime(),
      lastExecutionTime: this.executionLogs[0]?.startTime
    }
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(executionLog: ExecutionLog): void {
    this.statistics.totalExecutions++
    this.statistics.totalTasksProcessed += executionLog.processedCount
    this.statistics.totalTasksFailed += executionLog.failedCount

    if (executionLog.status === 'completed') {
      this.statistics.successfulExecutions++
    } else {
      this.statistics.failedExecutions++
    }

    // 计算平均处理时间
    if (executionLog.duration) {
      const totalTime = this.statistics.averageProcessingTime * (this.statistics.totalExecutions - 1) + executionLog.duration
      this.statistics.averageProcessingTime = totalTime / this.statistics.totalExecutions
    }
  }

  /**
   * 启动定时调度器（可选功能）
   */
  private startScheduler(): void {
    // 如果需要定时执行任务，可以在这里实现
    // 例如：每5分钟检查一次待处理任务
    const intervalMinutes = parseInt(process.env.TASK_SCHEDULER_INTERVAL || '0')
    
    if (intervalMinutes > 0) {
      logger.info('启动任务定时调度器', {
        intervalMinutes,
        action: 'scheduler_init'
      })

      this.schedulerInterval = setInterval(async () => {
        if (!this.isProcessing) {
          try {
            await this.executeTaskProcessing('scheduler')
          } catch (error) {
            logger.error('定时任务执行失败', error as Error, {
              action: 'scheduled_task_error'
            })
          }
        }
      }, intervalMinutes * 60 * 1000)
    }
  }

  /**
   * 停止调度器
   */
  stopScheduler(): void {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval)
      this.schedulerInterval = undefined
      logger.info('任务调度器已停止', { action: 'scheduler_stop' })
    }
  }

  /**
   * 清空执行日志
   */
  clearExecutionLogs(): void {
    this.executionLogs = []
    logger.info('执行日志已清空', { action: 'logs_cleared' })
  }

  /**
   * 重置统计信息
   */
  resetStatistics(): void {
    this.statistics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageProcessingTime: 0,
      totalTasksProcessed: 0,
      totalTasksFailed: 0,
      uptime: 0
    }
    this.startTime = new Date()
    logger.info('统计信息已重置', { action: 'stats_reset' })
  }
}

// 创建单例实例
export const taskScheduler = new TaskScheduler()

// 优雅关闭处理
// process.on('SIGTERM', () => {
//   // logger.info('收到SIGTERM信号，正在关闭任务调度器...')
//   taskScheduler.stopScheduler()
// })

// process.on('SIGINT', () => {
//   // logger.info('收到SIGINT信号，正在关闭任务调度器...')
//   taskScheduler.stopScheduler()
// })
