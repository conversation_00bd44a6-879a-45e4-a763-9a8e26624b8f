import winston from 'winston'
import path from 'path'
import fs from 'fs'

// 确保日志目录存在
const logDir = path.join(process.cwd(), 'logs')
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true })
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`
    
    // 如果有额外的元数据，添加到日志中
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`
    }
    
    // 如果有错误堆栈，添加到日志中
    if (stack) {
      log += `\n${stack}`
    }
    
    return log
  })
)

// 控制台格式（带颜色）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta, null, 2)}`
    }
    
    if (stack) {
      log += `\n${stack}`
    }
    
    return log
  })
)

// 创建winston logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'triage-app' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      )
    }),
    
    // 组合日志文件（所有级别）
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      )
    }),
    
    // 应用日志文件（info及以上级别）
    new winston.transports.File({
      filename: path.join(logDir, 'app.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 10,
      format: logFormat
    })
  ]
})

// 在非生产环境下，同时输出到控制台
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }))
}

// 在生产环境下，也可以选择性地输出到控制台
if (process.env.NODE_ENV === 'production' && process.env.CONSOLE_LOG === 'true') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'warn' // 生产环境只输出警告和错误到控制台
  }))
}

// 导出logger实例
export default logger

// 导出便捷方法
export const log = {
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  info: (message: string, meta?: any) => logger.info(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),
  verbose: (message: string, meta?: any) => logger.verbose(message, meta)
}

// 任务处理专用日志方法
export const taskLog = {
  start: (taskId: number, title: string) => 
    logger.info(`🚀 开始处理任务`, { taskId, title, action: 'task_start' }),
  
  success: (taskId: number, title: string, meta?: any) => 
    logger.info(`✅ 任务处理成功`, { taskId, title, action: 'task_success', ...meta }),
  
  error: (taskId: number, title: string, error: Error | string, meta?: any) => 
    logger.error(`❌ 任务处理失败`, { 
      taskId, 
      title, 
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      action: 'task_error',
      ...meta 
    }),
  
  step: (taskId: number, step: string, meta?: any) => 
    logger.info(`📝 任务步骤`, { taskId, step, action: 'task_step', ...meta }),
  
  warning: (taskId: number, message: string, meta?: any) => 
    logger.warn(`⚠️ 任务警告`, { taskId, message, action: 'task_warning', ...meta })
}

// 邮件处理专用日志方法
export const emailLog = {
  send: (to: string, subject: string, meta?: any) => 
    logger.info(`📧 发送邮件`, { to, subject, action: 'email_send', ...meta }),
  
  sendSuccess: (to: string, subject: string, meta?: any) => 
    logger.info(`✅ 邮件发送成功`, { to, subject, action: 'email_send_success', ...meta }),
  
  sendError: (to: string, subject: string, error: Error | string, meta?: any) => 
    logger.error(`❌ 邮件发送失败`, { 
      to, 
      subject, 
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      action: 'email_send_error',
      ...meta 
    }),
  
  receive: (from: string, subject: string, meta?: any) => 
    logger.info(`📨 接收邮件`, { from, subject, action: 'email_receive', ...meta })
}

// API请求日志方法
export const apiLog = {
  request: (method: string, url: string, meta?: any) => 
    logger.info(`🌐 API请求`, { method, url, action: 'api_request', ...meta }),
  
  response: (method: string, url: string, status: number | string | any, duration?: number, meta?: any) => 
    logger.info(`📡 API响应`, { method, url, status, duration, action: 'api_response', ...meta }),
  
  error: (method: string, url: string, error: Error | string, meta?: any) => 
    logger.error(`❌ API错误`, { 
      method, 
      url, 
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      action: 'api_error',
      ...meta 
    })
}

// 数据库操作日志方法
export const dbLog = {
  query: (operation: string, table: string, meta?: any) => 
    logger.debug(`🗄️ 数据库查询`, { operation, table, action: 'db_query', ...meta }),
  
  error: (operation: string, table: string, error: Error | string, meta?: any) => 
    logger.error(`❌ 数据库错误`, { 
      operation, 
      table, 
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      action: 'db_error',
      ...meta 
    })
}
