'use client';

import { useState } from 'react';
import { ReactElement } from 'react';
import { Button } from '@/components/ui/Button';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';

export interface ConfirmDialogProps {
  isOpen: boolean;
  title?: string;
  message: string;
  type?: 'info' | 'warning' | 'error' | 'success';
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

export function ConfirmDialog({
  isOpen,
  title,
  message,
  type = 'info',
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  onCancel,
  loading = false,
}: ConfirmDialogProps) {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Info className="h-6 w-6 text-blue-500" />;
    }
  };

  const getButtonVariant = () => {
    switch (type) {
      case 'warning':
        return 'warning';
      case 'error':
        return 'destructive';
      case 'success':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onCancel}
      />
      
      {/* 对话框 */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <div className="flex items-start gap-4">
          {getIcon()}
          <div className="flex-1 min-w-0">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {title}
              </h3>
            )}
            <p className="text-sm text-gray-700 mb-6">
              {message}
            </p>
            
            <div className="flex gap-3 justify-end">
              <Button
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                {cancelText}
              </Button>
              <Button
                variant={getButtonVariant()}
                onClick={onConfirm}
                disabled={loading}
              >
                {loading ? '处理中...' : confirmText}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export interface ConfirmDialogContextType {
  showConfirm: (options: Omit<ConfirmDialogProps, 'isOpen' | 'onConfirm' | 'onCancel'>) => Promise<boolean>;
  showWarning: (message: string, title?: string) => Promise<boolean>;
  showError: (message: string, title?: string) => Promise<boolean>;
  showInfo: (message: string, title?: string) => Promise<boolean>;
  ConfirmDialogContainer: () => ReactElement;
}

export function useConfirmDialog(): ConfirmDialogContextType {
  const [dialogState, setDialogState] = useState<{
    isOpen: boolean;
    props: Omit<ConfirmDialogProps, 'isOpen' | 'onConfirm' | 'onCancel'>;
    resolve?: (value: boolean) => void;
  }>({
    isOpen: false,
    props: { message: '' },
  });

  const showConfirm = (props: Omit<ConfirmDialogProps, 'isOpen' | 'onConfirm' | 'onCancel'>): Promise<boolean> => {
    return new Promise((resolve) => {
      setDialogState({
        isOpen: true,
        props,
        resolve,
      });
    });
  };

  const handleConfirm = () => {
    dialogState.resolve?.(true);
    setDialogState(prev => ({ ...prev, isOpen: false }));
  };

  const handleCancel = () => {
    dialogState.resolve?.(false);
    setDialogState(prev => ({ ...prev, isOpen: false }));
  };

  const showWarning = (message: string, title?: string): Promise<boolean> => {
    return showConfirm({ message, title, type: 'warning' });
  };

  const showError = (message: string, title?: string): Promise<boolean> => {
    return showConfirm({ message, title, type: 'error' });
  };

  const showInfo = (message: string, title?: string): Promise<boolean> => {
    return showConfirm({ message, title, type: 'info' });
  };

  const ConfirmDialogContainer = () => (
    <ConfirmDialog
      {...dialogState.props}
      isOpen={dialogState.isOpen}
      onConfirm={handleConfirm}
      onCancel={handleCancel}
    />
  );

  return {
    showConfirm,
    showWarning,
    showError,
    showInfo,
    ConfirmDialogContainer,
  } as ConfirmDialogContextType;
}
