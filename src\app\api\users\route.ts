import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getCurrentUser, hasPermission } from '@/lib/auth'
import { UserRole } from 'generated-prisma'

const createUserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  name: z.string().min(1, '请输入用户姓名'),
  roles: z.array(z.enum(['WM', 'Triage', 'PM'])).min(1, '请至少选择一个角色'),
  department: z.string().optional(),
  domainAccount: z.string().min(1, '请输入域账号'),
  dailyReportMin: z.number().min(0).optional(),
  dailyReportMax: z.number().min(0).optional()
})

const updateUserSchema = createUserSchema.partial().extend({
  id: z.number(),
  isActive: z.boolean().optional()
})

/**
 * 获取用户列表
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const search = url.searchParams.get('search') || ''
    const role = url.searchParams.get('role') as UserRole | null

    const skip = (page - 1) * limit

    const where = {
      AND: [
        search ? {
          OR: [
            { name: { contains: search } },
            { email: { contains: search } },
            { department: { contains: search } }
          ]
        } : {},
        role ? { roles: { path: '$', array_contains: role } } : {}
      ]
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },

      }),
      prisma.user.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        users: users,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get users failed:', error)
    return NextResponse.json({
      success: false,
      error: '获取用户列表失败'
    }, { status: 500 })
  }
}

/**
 * 创建用户
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const { email, name, roles, department, domainAccount, dailyReportMin, dailyReportMax } = createUserSchema.parse(body)

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: '该邮箱已被使用'
      }, { status: 400 })
    }

    // 创建用户
    const newUser = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        name,
        roles: roles,
        department,
        domainAccount,
        dailyReportMin,
        dailyReportMax
      }
    })



    return NextResponse.json({
      success: true,
      data: {
        user: newUser
      }
    })

  } catch (error) {
    console.error('Create user failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: '创建用户失败'
    }, { status: 500 })
  }
}

/**
 * 更新用户
 */
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const { id, email, name, roles, department, domainAccount, dailyReportMin, dailyReportMax, isActive } = updateUserSchema.parse(body)

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        error: '用户不存在'
      }, { status: 404 })
    }

    // 如果更新邮箱，检查是否重复
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
      })

      if (emailExists) {
        return NextResponse.json({
          success: false,
          error: '该邮箱已被使用'
        }, { status: 400 })
      }
    }

    // 更新用户基本信息
    await prisma.user.update({
      where: { id },
      data: {
        ...(email && { email: email.toLowerCase() }),
        ...(name && { name }),
        ...(roles && { roles: roles }),
        ...(department !== undefined && { department }),
        ...(domainAccount !== undefined && { domainAccount }),
        ...(dailyReportMin !== undefined && { dailyReportMin }),
        ...(dailyReportMax !== undefined && { dailyReportMax }),
        ...(isActive !== undefined && { isActive })
      }
    })



    // 获取更新后的用户信息
    const updatedUserData = await prisma.user.findUnique({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      data: {
        user: updatedUserData
      },
      message: '用户更新成功'
    })

  } catch (error) {
    console.error('Update user failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: '更新用户失败'
    }, { status: 500 })
  }
}
