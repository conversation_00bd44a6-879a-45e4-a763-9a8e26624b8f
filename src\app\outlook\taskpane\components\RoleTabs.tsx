import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Send, Mail, Users, Settings, CheckSquare } from 'lucide-react';
import { UserInfo, Project } from '../types';
import { EmailSelector } from './EmailSelector';
import { EmailList } from './EmailList';
import { ProjectList } from './ProjectList';
import { TaskList } from './TaskList';
import type { FullEmailInfo } from '../stores/EmailStore';

interface RoleTabsProps {
  userInfo: UserInfo;
  projects: Project[];
  selectedEmails: FullEmailInfo[];
  selectedEmailsCount: number;
  onCheckEmails: () => void;
  onClearEmails: () => void;
  onRemoveEmail: (emailId: string) => void;
  onDistribute: () => Promise<void>;
  onProcessEmails: (projectId: string) => Promise<void>;
}

export function RoleTabs({
  userInfo,
  projects,
  selectedEmails,
  selectedEmailsCount,
  onCheckEmails,
  onClearEmails,
  onRemoveEmail,
  onDistribute,
  onProcessEmails
}: RoleTabsProps) {
  // 获取用户可用的角色（只处理WM和Triage）
  const availableRoles = userInfo.roles.filter(role => role === 'WM' );

  // 添加任务管理选项卡
  const availableTabs = [...availableRoles, 'Tasks'] as const;
  type TabType = 'WM' | 'Triage' | 'Tasks';

  // 如果没有可用角色，默认显示任务选项卡
  const [activeTab, setActiveTab] = useState<TabType>(
    availableRoles.includes('WM') ? 'WM' :  'Tasks'
  ); 

  return (
    <div className="space-y-4">
      {/* Tab 导航 - 始终显示，包括任务管理 */}
      <Card>
        <CardContent className="p-3">
          <div className="flex gap-2 flex-wrap">
            {availableRoles.map((role) => (
              <Button
                key={role}
                variant={activeTab === role ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab(role)}
                className="flex items-center gap-2"
              >
                {role === 'WM' ? (
                  <Send className="h-4 w-4" />
                ) : (
                  <Mail className="h-4 w-4" />
                )}
                邮件分发
              </Button>
            ))}
            {/* 任务管理选项卡 */}
            <Button
              variant={activeTab === 'Tasks' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('Tasks')}
              className="flex items-center gap-2"
            >
              <CheckSquare className="h-4 w-4" />
              任务管理
            </Button>
          </div>
        </CardContent>
      </Card>

     

      {/* 角色特定功能面板 */}
      {activeTab === 'WM' && (
        <div className="space-y-4">
          <EmailSelector
            selectedEmailsCount={selectedEmailsCount}
            onCheckEmails={onCheckEmails}
            onClearEmails={onClearEmails}
          />

          {/* 选中的邮件列表 */}
          <EmailList
            selectedEmails={selectedEmails}
            onRemoveEmail={onRemoveEmail}
            onDistribute={onDistribute}
          />

          {/* 项目列表 */}
          <ProjectList
            projects={projects}
          />
        </div>
      )}
 

      {/* 任务管理面板 */}
      {activeTab === 'Tasks' && (
        <div className="space-y-4">
          <TaskList />
        </div>
      )}
    </div>
  );
}
