import { AIExtractor, ExtractedSAEInfo } from '@/lib/services/aiExtractor'
import { readFileSync } from 'fs'
import { join } from 'path'


describe('AIExtractor', () => {
  let aiExtractor: AIExtractor
  let testText: string

  beforeAll(() => {
    // 读取测试数据
    const testDataPath = join(process.cwd(), 'extractedText.md')
    testText = readFileSync(testDataPath, 'utf-8')
  })

  beforeEach(() => {
    aiExtractor = new AIExtractor()
  })

  describe('extractSAEInfo', () => {
    it('应该成功提取SAE信息', async () => {

      const result = await aiExtractor.extractSAEInfo(testText)
      console.log(result.data)
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.data?.protocolNumber).toBe('HX008/MRG003-C001')
      expect(result.data?.learnedDate).toBe('2025-04-23')
      expect(result.data?.centerName).toBe('中山大学肿瘤防治中心')
      expect(result.data?.subjectNumber).toBe('101136/E101125')
      expect(result.data?.eventName).toBe('大疱性类天疱疮')
      expect(result.data?.seriousness).toBe('需要住院')
      expect(result.data?.eventType).toBe('SAE')
      expect(result.data?.causality).toContain('可能相关')
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.processingTime).toBeGreaterThan(0)
    })

  })
})
