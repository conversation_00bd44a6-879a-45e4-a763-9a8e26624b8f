'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Mo<PERSON>Footer } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface Project {
  id?: number
  projectCode: string
  projectName: string
  sponsor?: string
  studyTitle?: string
  pvEmail?: string
  isActive?: boolean
  userIds?:number[]
  userRoles?: Array<{
    userId: number
    roleInProject: string
    isPrimary: boolean
  }>
  users?: Array<{
    id: number
    name: string
    email: string
    roles: string[]
    roleInProject: string
    isPrimary: boolean
  }>
}

interface User {
  id: number
  name: string
  email: string
  roles: string[]
}

const projectRoleOptions = [
  { value: 'DE', label: 'Data Entry' },
  { value: 'QC', label: 'Quality Control' },
  { value: 'Triage', label: 'Triage' }
]

interface ProjectFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (project: Partial<Project>) => Promise<void>
  project?: Project | null
  mode: 'create' | 'edit'
}

export function ProjectForm({ isOpen, onClose, onSubmit, project, mode }: ProjectFormProps) {
  const [formData, setFormData] = useState<Partial<Project>>({
    projectCode: '',
    projectName: '',
    sponsor: '',
    studyTitle: '',
    pvEmail: '',
    isActive: true
  })
  const [users, setUsers] = useState<User[]>([])
  const [selectedUsers, setSelectedUsers] = useState<Array<{
    userId: number
    roleInProject: string
    isPrimary: boolean
  }>>([])
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (project && mode === 'edit') {
      setFormData({
        id: project.id,
        projectCode: project.projectCode,
        projectName: project.projectName,
        sponsor: project.sponsor || '',
        studyTitle: project.studyTitle || '',
        pvEmail: project.pvEmail || '',
        isActive: project.isActive
      })
      setSelectedUsers(project.users?.map(u => ({
        userId: u.id,
        roleInProject: u.roleInProject,
        isPrimary: u.isPrimary
      })) || [])
    } else {
      setFormData({
        projectCode: '',
        projectName: '',
        sponsor: '',
        studyTitle: '',
        pvEmail: '',
        isActive: true
      })
      setSelectedUsers([])
    }
    setErrors({})
  }, [project, mode, isOpen])

  useEffect(() => {
    const loadUsers = async () => {
      try {
        const response = await fetch('/api/users?limit=100', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          setUsers(result.data.users)
        }
      } catch (error) {
        console.error('Failed to load users:', error)
      }
    }

    if (isOpen) {
      loadUsers()
    }
  }, [isOpen])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.projectCode?.trim()) {
      newErrors.projectCode = '项目编号不能为空'
    }

    if (!formData.projectName?.trim()) {
      newErrors.projectName = '项目名称不能为空'
    }

    if (formData.pvEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.pvEmail)) {
      newErrors.pvEmail = '请输入有效的邮箱地址'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      await onSubmit({
        ...formData,
        userIds: selectedUsers.map(u => u.userId),
        userRoles: selectedUsers
      })
      onClose()
    } catch (error) {
      console.error('Submit failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUserToggle = (userId: number) => {
    setSelectedUsers(prev => {
      const existing = prev.find(u => u.userId === userId)
      if (existing) {
        return prev.filter(u => u.userId !== userId)
      } else {
        return [...prev, {
          userId,
          roleInProject: 'Triage',
          isPrimary: false
        }]
      }
    })
  }

  const handleUserRoleChange = (userId: number, roleInProject: string) => {
    setSelectedUsers(prev =>
      prev.map(u =>
        u.userId === userId
          ? { ...u, roleInProject }
          : u
      )
    )
  }

  const handlePrimaryToggle = (userId: number) => {
    setSelectedUsers(prev =>
      prev.map(u =>
        u.userId === userId
          ? { ...u, isPrimary: !u.isPrimary }
          : u
      )
    )
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? '新建项目' : '编辑项目'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="项目编号 *"
            value={formData.projectCode || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, projectCode: e.target.value }))}
            error={errors.projectCode}
            placeholder="例如：MRG004A-001"
          />
          
          <Input
            label="项目名称 *"
            value={formData.projectName || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
            error={errors.projectName}
            placeholder="例如：MRG004A临床试验"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="申办方"
            value={formData.sponsor || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, sponsor: e.target.value }))}
            placeholder="例如：Example Pharma"
          />
          
          <Input
            label="PV邮箱"
            type="email"
            value={formData.pvEmail || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, pvEmail: e.target.value }))}
            error={errors.pvEmail}
            placeholder="例如：<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            研究标题
          </label>
          <textarea
            value={formData.studyTitle || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, studyTitle: e.target.value }))}
            rows={3}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            placeholder="例如：An Open-Label, Multi-center, Phase I/II Dose Escalation and Expansion Study"
          />
        </div>

        {mode === 'edit' && (
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive || false}
              onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
              项目状态：活跃
            </label>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            项目成员分配
          </label>
          <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md p-3 space-y-3">
            {users.map(user => {
              const assignment = selectedUsers.find(u => u.userId === user.id)
              const isSelected = !!assignment

              return (
                <div key={user.id} className="border-b border-gray-100 pb-3 last:border-b-0">
                  <div className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      id={`user-${user.id}`}
                      checked={isSelected}
                      onChange={() => handleUserToggle(user.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor={`user-${user.id}`} className="ml-2 block text-sm font-medium text-gray-900">
                      {user.name} ({user.email}) - {user.roles.join(', ')}
                    </label>
                  </div>

                  {isSelected && (
                    <div className="ml-6 grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">项目角色</label>
                        <select
                          value={assignment.roleInProject}
                          onChange={(e) => handleUserRoleChange(user.id, e.target.value)}
                          className="block w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white"
                        >
                          {projectRoleOptions.map(option => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`primary-${user.id}`}
                          checked={assignment.isPrimary}
                          onChange={() => handlePrimaryToggle(user.id)}
                          className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`primary-${user.id}`} className="ml-1 block text-xs text-gray-700">
                          主要负责人
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        <ModalFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
          >
            {mode === 'create' ? '创建项目' : '保存更改'}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  )
}
