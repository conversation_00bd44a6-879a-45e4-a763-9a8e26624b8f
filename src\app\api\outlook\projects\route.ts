import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getCurrentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 从cookie获取当前用户
    const user = await getCurrentUser(request);

    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 获取项目列表
    const projects = await prisma.project.findMany({
      where: {
        isActive: true,
      },
      select: {
        id: true,
        projectCode: true,
        projectName: true,
        sponsor: true,
        studyTitle: true,
        pvEmail: true,
        isActive: true, 
        description: true,
        userProjects: {
          select: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // 格式化项目数据
    const formattedProjects = projects.map(project => ({
      id: project.id.toString(),
      name: project.projectName,
      code: project.projectCode,
      description: project.studyTitle || project.sponsor || '暂无描述',
      isActive: project.isActive,
      memberCount: project.userProjects.length,
      pvEmail: project.pvEmail, 
    }));

    return NextResponse.json({
      success: true,
      projects: formattedProjects,
    });

  } catch (error) {
    console.error('获取项目列表失败:', error);
    return NextResponse.json(
      { error: '获取项目列表失败' },
      { status: 500 }
    );
  }
}
