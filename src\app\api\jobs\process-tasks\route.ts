import { NextRequest, NextResponse } from 'next/server'
import { taskProcessorService } from '@/lib/services/taskProcessorService'
import { taskScheduler } from '@/lib/jobs/taskScheduler'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'
import logger, { apiLog } from '@/lib/logger'

/**
 * 手动触发任务处理
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('POST', '/api/jobs/process-tasks')

  try {
    // 验证用户权限 - 只有WM和PM角色可以触发
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      apiLog.response('POST', '/api/jobs/process-tasks', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    logger.info('用户触发任务处理', {
      userId: authResult.user?.id,
      userName: authResult.user?.name,
      action: 'manual_task_trigger'
    })

    // 检查是否已有任务在处理
    if (taskScheduler.isCurrentlyProcessing()) {
      logger.warn('任务处理已在进行中，拒绝重复请求', {
        userId: authResult.user?.id,
        action: 'task_processing_busy'
      })
      apiLog.response('POST', '/api/jobs/process-tasks', 409, Date.now() - startTime, {
        message: 'Task processing already in progress'
      })
      return NextResponse.json({
        success: false,
        message: '任务处理正在进行中，请稍后再试',
        isProcessing: true
      })
    }

    // 执行任务处理
    logger.info('开始执行任务处理', {
      userId: authResult.user?.id,
      action: 'task_processing_start'
    })
    const executionLog = await taskScheduler.executeTaskProcessing()

    logger.info('任务处理执行完成', {
      userId: authResult.user?.id,
      executionId: executionLog.id,
      processedCount: executionLog.processedCount,
      failedCount: executionLog.failedCount,
      duration: executionLog.duration,
      status: executionLog.status,
      action: 'task_processing_complete'
    })

    apiLog.response('POST', '/api/jobs/process-tasks', 200, Date.now() - startTime, {
      processedCount: executionLog.processedCount,
      failedCount: executionLog.failedCount,
      status: executionLog.status
    })

    return NextResponse.json({
      success: executionLog.status === 'completed',
      message: executionLog.status === 'completed' ? '任务处理完成' : '任务处理失败',
      data: {
        executionId: executionLog.id,
        processedCount: executionLog.processedCount,
        failedCount: executionLog.failedCount,
        duration: executionLog.duration,
        status: executionLog.status,
        error: executionLog.error
      }
    })

  } catch (error) {
    apiLog.error('POST', '/api/jobs/process-tasks', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      {
        success: false,
        error: '任务处理失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 获取任务处理状态和统计信息
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('GET', '/api/jobs/process-tasks')

  try {
    // 验证用户权限
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM, UserRole.Triage])
    if (!authResult.success) {
      apiLog.response('GET', '/api/jobs/process-tasks', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    logger.debug('获取任务处理状态', {
      userId: authResult.user?.id,
      action: 'get_task_status'
    })

    // 获取处理统计信息
    const processingStats = await taskProcessorService.getProcessingStats()

    // 获取任务处理器状态
    const taskProcessorStatus = taskScheduler.getSchedulerStatus()

    // 获取最近执行状态
    const lastExecutionStatus = taskScheduler.getLastExecutionStatus()

    // 获取执行日志
    const executionLogs = taskScheduler.getExecutionLogs(10)

    // 获取统计信息
    const statistics = taskScheduler.getStatistics()

    apiLog.response('GET', '/api/jobs/process-tasks', 200, Date.now() - startTime, {
      totalTasks: processingStats.totalTasks,
      pendingTasks: processingStats.pendingTasks,
      isProcessing: taskScheduler.isCurrentlyProcessing()
    })

    return NextResponse.json({
      success: true,
      data: {
        processingStats,
        taskProcessorStatus,
        lastExecutionStatus,
        executionLogs,
        statistics,
        isCurrentlyProcessing: taskScheduler.isCurrentlyProcessing()
      }
    })

  } catch (error) {
    apiLog.error('GET', '/api/jobs/process-tasks', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      {
        success: false,
        error: '获取状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
