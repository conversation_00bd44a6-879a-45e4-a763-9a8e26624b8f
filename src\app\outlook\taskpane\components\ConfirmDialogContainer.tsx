'use client';

import React from 'react';
import { observer } from 'mobx-react-lite';
import { useNotificationStore } from '../stores';

const ConfirmDialogContainer: React.FC = observer(() => {
  const notificationStore = useNotificationStore();

  if (notificationStore.confirmDialogs.length === 0) {
    return null;
  }

  // 只显示第一个对话框（模态）
  const dialog = notificationStore.confirmDialogs[0];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50"
        onClick={() => notificationStore.cancelConfirm(dialog.id)}
      />
      
      {/* 对话框内容 */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6 animate-in zoom-in-95">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg 
              className="w-6 h-6 text-amber-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              确认操作
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {dialog.message}
            </p>
          </div>
        </div>
        
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={() => notificationStore.resolveConfirm(dialog.id, false)}
          >
            取消
          </button>
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={() => notificationStore.resolveConfirm(dialog.id, true)}
          >
            确认
          </button>
        </div>
      </div>
    </div>
  );
});

ConfirmDialogContainer.displayName = 'ConfirmDialogContainer';

export { ConfirmDialogContainer };
