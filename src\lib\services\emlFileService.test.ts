import { EmlFileService, EmailDetailsForEml } from './emlFileService'
import { promises as fs } from 'fs'
import path from 'path'

describe('EmlFileService', () => {
  let emlFileService: EmlFileService
  const testUploadDir = path.join(process.cwd(), 'test-uploads')

  beforeEach(() => {
    emlFileService = new EmlFileService()
  })

  afterEach(async () => {
    // 清理测试文件
    try {
      await fs.rmdir(testUploadDir, { recursive: true })
    } catch (error) {
      // 忽略清理错误
    }
  })

  test('应该成功生成EML文件', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '<EMAIL>',
      subject: '测试邮件主题',
      date: new Date().toUTCString(),
      body: '<html><body><h1>测试邮件内容</h1><p>这是一封测试邮件。</p></body></html>',
      attachments: [
        {
          name: 'test.txt',
          size: 100,
          contentType: 'text/plain',
          content: Buffer.from('测试附件内容').toString('base64'),
          isInline: false,
          id: 'att1',
          encoding: 'base64'
        }
      ]
    }

    const mailId = 'test-mail-id-123'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    // 验证返回结果
    expect(result).toHaveProperty('filePath')
    expect(result).toHaveProperty('fileName')
    expect(result).toHaveProperty('fileSize')
    expect(result).toHaveProperty('fileHash')
    expect(result.fileName).toContain('.eml')
    expect(result.fileSize).toBeGreaterThan(0)

    // 验证文件是否存在
    const fileExists = await fs.access(result.filePath).then(() => true).catch(() => false)
    expect(fileExists).toBe(true)

    // 验证文件内容
    const fileContent = await fs.readFile(result.filePath, 'utf8')
    expect(fileContent).toContain('From: <EMAIL>')
    expect(fileContent).toContain('To: <EMAIL>')
    expect(fileContent).toContain('Cc: <EMAIL>')
    expect(fileContent).toContain('Subject: 测试邮件主题')
    expect(fileContent).toContain('测试邮件内容')
    expect(fileContent).toContain('Content-Type: text/plain')
    expect(fileContent).toContain('filename="test.txt"')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该正确处理特殊字符的邮件主题', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '包含特殊字符的主题: <test> "quotes" /slash\\backslash',
      date: new Date().toUTCString(),
      body: '<html><body>测试内容</body></html>',
      attachments: []
    }

    const mailId = 'test-special-chars'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    // 验证文件名不包含特殊字符
    expect(result.fileName).not.toMatch(/[<>:"/\\|?*]/)
    expect(result.fileName).toContain('.eml')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该正确处理没有附件的邮件', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '无附件邮件',
      date: new Date().toUTCString(),
      body: '<html><body>这是一封没有附件的邮件</body></html>',
      attachments: []
    }

    const mailId = 'no-attachments'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    expect(result.fileSize).toBeGreaterThan(0)

    // 验证文件内容
    const fileContent = await fs.readFile(result.filePath, 'utf8')
    expect(fileContent).toContain('这是一封没有附件的邮件')
    expect(fileContent).not.toContain('Content-Disposition: attachment')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该正确读取EML文件内容', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '读取测试',
      date: new Date().toUTCString(),
      body: '<html><body>读取测试内容</body></html>',
      attachments: []
    }

    const mailId = 'read-test'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    // 读取文件内容
    const content = await emlFileService.readEmlFile(result.filePath)
    expect(content).toContain('读取测试内容')
    expect(content).toContain('From: <EMAIL>')

    // 获取文件信息
    const fileInfo = await emlFileService.getEmlFileInfo(result.filePath)
    expect(fileInfo).not.toBeNull()
    expect(fileInfo?.size).toBe(result.fileSize)
    expect(fileInfo?.hash).toBe(result.fileHash)

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })
})
