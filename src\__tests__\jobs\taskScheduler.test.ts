import { taskScheduler } from '@/lib/jobs/taskScheduler'
import { taskProcessorService } from '@/lib/services/taskProcessorService'

// Mock taskProcessorService
jest.mock('@/lib/services/taskProcessorService', () => ({
  taskProcessorService: {
    processAllPendingTasks: jest.fn()
  }
}))

const mockTaskProcessorService = taskProcessorService as jest.Mocked<typeof taskProcessorService>

describe('TaskScheduler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // 清空执行日志和重置统计信息
    taskScheduler.clearExecutionLogs()
    taskScheduler.resetStatistics()
  })

  describe('基本功能', () => {
    test('应该正确初始化', () => {
      expect(taskScheduler.isCurrentlyProcessing()).toBe(false)
      
      const status = taskScheduler.getSchedulerStatus()
      expect(status.isRunning).toBe(false)
      expect(status.totalExecutions).toBe(0)
      
      const stats = taskScheduler.getStatistics()
      expect(stats.totalExecutions).toBe(0)
      expect(stats.successfulExecutions).toBe(0)
      expect(stats.failedExecutions).toBe(0)
    })

    test('应该返回空的执行日志', () => {
      const logs = taskScheduler.getExecutionLogs()
      expect(logs).toEqual([])
      
      const lastExecution = taskScheduler.getLastExecutionStatus()
      expect(lastExecution).toBeNull()
    })
  })

  describe('任务执行', () => {
    test('应该成功执行任务处理', async () => {
      // Mock 成功的任务处理结果
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 5,
        failedCount: 1,
        results: []
      })

      const executionLog = await taskScheduler.executeTaskProcessing('test-user')

      expect(executionLog.status).toBe('completed')
      expect(executionLog.processedCount).toBe(5)
      expect(executionLog.failedCount).toBe(1)
      expect(executionLog.triggeredBy).toBe('test-user')
      expect(executionLog.duration).toBeGreaterThan(0)
      expect(executionLog.id).toBeDefined()
    })

    test('应该处理任务处理失败的情况', async () => {
      // Mock 失败的任务处理结果
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: false,
        processedCount: 2,
        failedCount: 3,
        results: []
      })

      const executionLog = await taskScheduler.executeTaskProcessing('test-user')

      expect(executionLog.status).toBe('failed')
      expect(executionLog.processedCount).toBe(2)
      expect(executionLog.failedCount).toBe(3)
      expect(executionLog.error).toBe('批量任务处理失败')
    })

    test('应该处理异常情况', async () => {
      // Mock 抛出异常
      const error = new Error('数据库连接失败')
      mockTaskProcessorService.processAllPendingTasks.mockRejectedValue(error)

      const executionLog = await taskScheduler.executeTaskProcessing('test-user')

      expect(executionLog.status).toBe('failed')
      expect(executionLog.error).toBe('数据库连接失败')
      expect(executionLog.duration).toBeGreaterThanOrEqual(0)
    })

    test('应该防止并发执行', async () => {
      // Mock 长时间运行的任务
      mockTaskProcessorService.processAllPendingTasks.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          success: true,
          processedCount: 1,
          failedCount: 0,
          results: []
        }), 100))
      )

      // 启动第一个任务
      const promise1 = taskScheduler.executeTaskProcessing('user1')
      
      // 尝试启动第二个任务（应该失败）
      await expect(taskScheduler.executeTaskProcessing('user2')).rejects.toThrow('任务处理已在进行中')
      
      // 等待第一个任务完成
      await promise1
      
      // 现在应该可以启动新任务
      const executionLog = await taskScheduler.executeTaskProcessing('user3')
      expect(executionLog.status).toBe('completed')
    })
  })

  describe('状态管理', () => {
    test('应该正确更新调度器状态', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 3,
        failedCount: 0,
        results: []
      })

      await taskScheduler.executeTaskProcessing('test-user')

      const status = taskScheduler.getSchedulerStatus()
      expect(status.totalExecutions).toBe(1)
      expect(status.successfulExecutions).toBe(1)
      expect(status.failedExecutions).toBe(0)
      expect(status.lastExecutionTime).toBeDefined()
    })

    test('应该正确更新统计信息', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 5,
        failedCount: 2,
        results: []
      })

      await taskScheduler.executeTaskProcessing('test-user')

      const stats = taskScheduler.getStatistics()
      expect(stats.totalExecutions).toBe(1)
      expect(stats.successfulExecutions).toBe(1)
      expect(stats.failedExecutions).toBe(0)
      expect(stats.totalTasksProcessed).toBe(5)
      expect(stats.totalTasksFailed).toBe(2)
      expect(stats.averageProcessingTime).toBeGreaterThan(0)
      expect(stats.uptime).toBeGreaterThan(0)
    })

    test('应该正确处理多次执行的统计', async () => {
      // 第一次执行 - 成功
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValueOnce({
        success: true,
        processedCount: 3,
        failedCount: 1,
        results: []
      })

      await taskScheduler.executeTaskProcessing('user1')

      // 第二次执行 - 失败
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValueOnce({
        success: false,
        processedCount: 1,
        failedCount: 2,
        results: []
      })

      await taskScheduler.executeTaskProcessing('user2')

      const stats = taskScheduler.getStatistics()
      expect(stats.totalExecutions).toBe(2)
      expect(stats.successfulExecutions).toBe(1)
      expect(stats.failedExecutions).toBe(1)
      expect(stats.totalTasksProcessed).toBe(4)
      expect(stats.totalTasksFailed).toBe(3)
    })
  })

  describe('执行日志管理', () => {
    test('应该正确记录执行日志', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 2,
        failedCount: 0,
        results: []
      })

      await taskScheduler.executeTaskProcessing('test-user')

      const logs = taskScheduler.getExecutionLogs()
      expect(logs).toHaveLength(1)
      expect(logs[0].triggeredBy).toBe('test-user')
      expect(logs[0].status).toBe('completed')

      const lastExecution = taskScheduler.getLastExecutionStatus()
      expect(lastExecution).toEqual(logs[0])
    })

    test('应该限制执行日志数量', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 1,
        failedCount: 0,
        results: []
      })

      // 执行多次任务
      for (let i = 0; i < 15; i++) {
        await taskScheduler.executeTaskProcessing(`user-${i}`)
      }

      const logs = taskScheduler.getExecutionLogs()
      expect(logs.length).toBeLessThanOrEqual(15)
      
      // 最新的执行应该在前面
      expect(logs[0].triggeredBy).toBe('user-14')
    })

    test('应该正确返回指定数量的日志', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 1,
        failedCount: 0,
        results: []
      })

      // 执行5次任务
      for (let i = 0; i < 5; i++) {
        await taskScheduler.executeTaskProcessing(`user-${i}`)
      }

      const logs3 = taskScheduler.getExecutionLogs(3)
      expect(logs3).toHaveLength(3)

      const logs10 = taskScheduler.getExecutionLogs(10)
      expect(logs10).toHaveLength(5) // 只有5条记录
    })
  })

  describe('工具方法', () => {
    test('应该能清空执行日志', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 1,
        failedCount: 0,
        results: []
      })

      await taskScheduler.executeTaskProcessing('test-user')
      expect(taskScheduler.getExecutionLogs()).toHaveLength(1)

      taskScheduler.clearExecutionLogs()
      expect(taskScheduler.getExecutionLogs()).toHaveLength(0)
      expect(taskScheduler.getLastExecutionStatus()).toBeNull()
    })

    test('应该能重置统计信息', async () => {
      mockTaskProcessorService.processAllPendingTasks.mockResolvedValue({
        success: true,
        processedCount: 5,
        failedCount: 2,
        results: []
      })

      await taskScheduler.executeTaskProcessing('test-user')
      
      let stats = taskScheduler.getStatistics()
      expect(stats.totalExecutions).toBe(1)
      expect(stats.totalTasksProcessed).toBe(5)

      taskScheduler.resetStatistics()
      
      stats = taskScheduler.getStatistics()
      expect(stats.totalExecutions).toBe(0)
      expect(stats.totalTasksProcessed).toBe(0)
      expect(stats.successfulExecutions).toBe(0)
      expect(stats.failedExecutions).toBe(0)
    })
  })
})
