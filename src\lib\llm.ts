import { Langfuse, observeOpenAI } from "langfuse";
import OpenAI from 'openai'  

export const langfuse = new Langfuse({
  secretKey: "sk-lf-48c677eb-8e6f-4674-bc3e-ebe35df038fb",
  publicKey: "pk-lf-0e06c0d6-7faa-46c6-9791-09eabd69073e",
  baseUrl: "http://copilot-ops-test.pharmaron.com"
}); 
 

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'dummy-key-for-build',
  baseURL: process.env.OPENAI_URL || 'dummy-key-for-build',
})
 
export const LLM = observeOpenAI(openai)

 